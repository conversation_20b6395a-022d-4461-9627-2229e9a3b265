# frozen_string_literal: true

#
# Copyright (C) 2018 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

module Types
  class SubmissionFilterInputType < Types::BaseInputObject
    graphql_name "SubmissionFilterInput"

    argument :states,
             [SubmissionStateType],
             required: false,
             default_value: DEFAULT_SUBMISSION_STATES

    argument :section_ids,
             [ID],
             required: false,
             prepare: GraphQLHelpers.relay_or_legacy_ids_prepare_func("Section")

    argument :due_between, DateTimeRangeType, required: false
    argument :graded_since, DateTimeType, required: false
    argument :submitted_since, DateTimeType, required: false
    argument :updated_since, DateTimeType, required: false
  end
end
