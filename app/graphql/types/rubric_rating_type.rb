# frozen_string_literal: true

#
# Copyright (C) 2019 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

module Types
  class RubricRatingType < ApplicationObjectType
    description "Possible rating for a rubric criterion"

    def initialize(object, context)
      @rubric_id = context[:rubric_id]
      super
    end

    implements Interfaces::LegacyIDInterface

    field :description, String, null: false
    field :long_description, String, null: true
    field :points, Float, null: false

    field :rubric_id, ID, null: false
    attr_reader :rubric_id
  end
end
