# frozen_string_literal: true

#
# Copyright (C) 2019 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

module Types
  class RubricAssociationType < ApplicationObjectType
    description "How a rubric is being used in a context"

    implements Interfaces::LegacyIDInterface

    field :hide_outcome_results, <PERSON><PERSON><PERSON>, null: false
    def hide_outcome_results
      !!object.hide_outcome_results
    end

    field :hide_points, <PERSON><PERSON><PERSON>, null: false
    def hide_points
      !!object.hide_points(current_user)
    end

    field :hide_score_total, <PERSON><PERSON><PERSON>, null: false
    def hide_score_total
      !!object.hide_score_total
    end

    field :use_for_grading, <PERSON><PERSON><PERSON>, null: false
    def use_for_grading
      !!object.use_for_grading
    end
    field :saved_comments, String, null: true
    def saved_comments
      object.summary_data&.dig(:saved_comments)&.to_json
    end
  end
end
