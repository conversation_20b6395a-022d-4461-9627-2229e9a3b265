class SimulationsController < ApplicationController
  before_action :require_context
  before_action :require_user
  before_action :check_authorized_action
  before_action :add_breadcrumbs

  helper_method :can_manage_simulations?

  def index
    add_crumb t('Simulations'), course_simulations_path(@context)
    @simulations = Simulation.where(course_id: @context.id)
  end

  def show
    # Handle individual simulation display/launch
    @simulation = find_simulation(params[:id])
    add_crumb t('Simulations'), course_simulations_path(@context)
    add_crumb @simulation[:title] || t('Simulation'), course_simulation_path(@context, @simulation)
    render :show
  end

  def create
    # Only allow teachers/admins
    unless @context.user_has_been_instructor?(@current_user) || @context.user_is_admin?(@current_user)
      redirect_to course_simulations_path(@context), alert: t('Not authorized')
      return
    end

    @simulation = Simulation.new(
      title: params[:simulation][:title],
      description: params[:simulation][:description],
      url: params[:simulation][:url],
      course_id: @context.id
    )

    if @simulation.save
      redirect_to course_simulations_path(@context), notice: t('Simulation added')
    else
      add_crumb t('Simulations'), course_simulations_path(@context)
      @simulations = Simulation.where(course_id: @context.id)
      render :index, alert: t('Could not add simulation')
    end
  end

  def destroy
    @simulation = Simulation.find(params[:id])
    unless can_manage_simulations?
      redirect_to course_simulations_path(@context), alert: t('Not authorized')
      return
    end
    @simulation.destroy
    redirect_to course_simulations_path(@context), notice: t('Simulation deleted')
  end

  def update
    @simulation = Simulation.find(params[:id])
    unless can_manage_simulations?
      redirect_to course_simulations_path(@context), alert: t('Not authorized')
      return
    end
    if @simulation.update(title: params[:simulation][:title], description: params[:simulation][:description], url: params[:simulation][:url])
      redirect_to course_simulations_path(@context), notice: t('Simulation updated')
    else
      redirect_to course_simulations_path(@context), alert: t('Could not update simulation')
    end
  end

  private

  def add_breadcrumbs
    # No course name breadcrumb - just set up for other breadcrumbs
  end

  def check_authorized_action
    authorized_action(@context, @current_user, :read)
  end

  def find_simulation(id)
    # Replace with your simulation finding logic
    # This could query a database, API, or static configuration
    Simulation.find_by(id: id, course_id: @context.id) || {}
  end

  def can_manage_simulations?
    @current_user && (
      (@context.respond_to?(:user_has_been_instructor?) && @context.user_has_been_instructor?(@current_user)) ||
      (@context.respond_to?(:user_is_admin?) && @context.user_is_admin?(@current_user))
    )
  end
end