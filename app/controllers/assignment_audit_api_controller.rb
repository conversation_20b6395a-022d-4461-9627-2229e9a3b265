# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

# @API Assignment Audit Log
#
# Query audit log of assignment events (notifications sent and due date changes).
#
# For each endpoint, a compound document is returned. The primary collection of
# event objects is paginated, ordered by date descending. Secondary collections
# of assignments, courses, users related to the returned events are also included.
#
# Assignment audit logs are stored for one year.
#
# @model AssignmentAuditEvent
#     {
#       "id": "AssignmentAuditEvent",
#       "description": "",
#       "properties": {
#         "id": {
#           "description": "ID of the event.",
#           "example": "e2b76430-27a5-4878-a1a5-6a8c75c3a76d",
#           "type": "string"
#         },
#         "created_at": {
#           "description": "timestamp of the event",
#           "example": "2025-06-01T15:00:00-06:00",
#           "type": "datetime"
#         },
#         "event_type": {
#           "description": "assignment event type ('notification_sent' or 'due_date_changed')",
#           "example": "notification_sent",
#           "type": "string",
#           "allowableValues": {
#             "values": [
#               "notification_sent",
#               "due_date_changed"
#             ]
#           }
#         },
#         "assignment_id": {
#           "description": "ID of the assignment associated with the event",
#           "example": 2319,
#           "type": "integer"
#         },
#         "course_id": {
#           "description": "ID of the course associated with the event",
#           "example": 101,
#           "type": "integer"
#         },
#         "user_id": {
#           "description": "ID of the user who performed the action (sent notification or changed due date)",
#           "example": 362,
#           "type": "integer"
#         },

#       }
#     }
#
class AssignmentAuditApiController < AuditorApiController
  include Api::V1::AssignmentAuditEvent
  
  # Override the parent class query_options to ensure we never pass event_type to auditors
  def query_options
    # Get the basic options from the parent class
    options = super
    
    # Ensure some filter is specified
    if options.empty? && !params[:assignment_id] && !params[:course_id] && !params[:user_id] && !params[:account_id] && 
       (!params[:event_type].present? || params[:event_type] == 'all')
      head(:bad_request)
    end
    
    # IMPORTANT: We never pass event_type to the auditors to avoid ActiveRecord association issues
    # Instead, we filter the events after fetching them
    options
  end

  # @API Query by assignment.
  #
  # List assignment audit events for a given assignment.
  #
  # @argument start_time [DateTime]
  #   The beginning of the time range from which you want events.
  #   Events are stored for one year.
  #
  # @argument end_time [DateTime]
  #   The end of the time range from which you want events.
  #
  # @returns [AssignmentAuditEvent]
  #
  def for_assignment
    @assignment = api_find(Assignment, params[:assignment_id])
    if authorized
      # Get all events first
      events = Auditors::Assignment.for_assignment(@assignment, query_options)
      render_events(events, @assignment)
    else
      render_unauthorized_action
    end
  end

  # @API Query by course.
  #
  # List assignment audit events for a given course.
  #
  # @argument start_time [DateTime]
  #   The beginning of the time range from which you want events.
  #   Events are stored for one year.
  #
  # @argument end_time [DateTime]
  #   The end of the time range from which you want events.
  #
  # @returns [AssignmentAuditEvent]
  #
  def for_course
    @course = api_find(@domain_root_account.all_courses, params[:course_id])
    if authorized
      begin
        events = Auditors::Assignment.for_course(@course, query_options)
        render_events(events, @course)
      rescue => e
        # Log the error and return an empty result set
        Rails.logger.error("Error fetching assignment audit events: #{e.message}")
        render json: { events: [] }
      end
    else
      render_unauthorized_action
    end
  end

  # @API Query by user.
  #
  # List assignment audit events for a given user 
  #
  # @argument start_time [DateTime]
  #   The beginning of the time range from which you want events.
  #   Events are stored for one year.
  #
  # @argument end_time [DateTime]
  #   The end of the time range from which you want events.
  #

  # @returns [AssignmentAuditEvent]
  #
  def for_user
    @user = api_find(User.active, params[:user_id])
    if authorized   
      begin
         events = Auditors::Assignment.for_user(@user, query_options)
         render_events(events, @user)
      rescue => e
        # Log the error and return an empty result set
        Rails.logger.error("Error fetching assignment audit events: #{e.message}")
        events = BookmarkedCollection.empty
      end
      
      render_events(events, @user)
    else
      render_unauthorized_action
    end
  end

  private

  def authorized
    @domain_root_account.grants_right?(@current_user, session, :view_assignment_changes)
  end

  def render_events(events, context, route = nil)
    route ||= polymorphic_url([:api_v1, :audit_assignment, context])
    events = Api.paginate(events, self, route)
    render json: assignment_audit_events_compound_json(events, @current_user, session)
  end
end
