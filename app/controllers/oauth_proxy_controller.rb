# frozen_string_literal: true

#
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

class OAuthProxyController < ApplicationController
  skip_before_action :load_user

  def redirect_proxy
    reject! t("The state parameter is required") and return unless params[:state]

    begin
      json = Canvas::Security.decode_jwt(params[:state])
      url = URI.parse(json["redirect_uri"])
      filtered_params = params.permit(:state, :code)
      url.query = url.query.blank? ? filtered_params.to_h.to_query : "#{url.query}&#{filtered_params.to_h.to_query}"
      redirect_to url.to_s
    rescue JSON::JWT::InvalidFormat, Canvas::Security::InvalidToken
      reject! t("Invalid state parameter") and return
    end
  end
end
