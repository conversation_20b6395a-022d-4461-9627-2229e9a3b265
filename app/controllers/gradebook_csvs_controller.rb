# frozen_string_literal: true

#
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

class GradebookCsvsController < ApplicationController
  before_action :require_context
  before_action :require_user

  def create
    if authorized_action(@context, @current_user, [:manage_grades, :view_all_grades])
      format = params[:format] || 'csv'
      export_options = build_export_options(format)
      filename = build_filename(format)

      attachment_progress = case format.downcase
                           when 'xlsx'
                             @context.gradebook_to_xlsx_in_background(filename, @current_user, export_options)
                           else
                             @context.gradebook_to_csv_in_background(filename, @current_user, export_options)
                           end

      render json: attachment_progress, status: :ok
    end
  end

  private

  def build_export_options(format)
    {
      include_sis_id: @context.grants_any_right?(@current_user, session, :read_sis, :manage_sis),
      grading_period_id: params[:grading_period_id],
      student_order: params[:student_order],
      current_view: params[:current_view],
      format: format
    }.tap do |options|
      if params[:assignment_order]
        options[:assignment_order] = params[:assignment_order].map(&:to_i)
      end

      if @context.account.allow_gradebook_show_first_last_names? &&
         Account.site_admin.feature_enabled?(:gradebook_show_first_last_names) &&
         params[:show_student_first_last_name]
        options[:show_student_first_last_name] = Canvas::Plugin.value_to_boolean(params[:show_student_first_last_name])
      end
    end
  end

  def build_filename(format)
    current_time = Time.zone.now.strftime("%FT%H%M")
    name = t("grades_filename", "Grades") + "-" + @context.short_name.to_s
    extension = format.downcase == 'xlsx' ? 'xlsx' : 'csv'
    "#{current_time}_#{name}.#{extension}".gsub(%r{/| }, "_")
  end
end