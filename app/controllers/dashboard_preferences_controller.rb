class DashboardPreferencesController < ApplicationController
  before_action :require_user
  
  # GET /dashboard/dashboard_preferences/:course_id
  def show
    course_id = params[:course_id].to_i
    preference_key = "details_expanded"
    
    value = @current_user.get_dashboard_card_preference(course_id, preference_key)
    
    render json: { 
      details_expanded: value.nil? ? false : value 
    }
  end
  
  # PUT /dashboard/dashboard_preferences/:course_id
  def update
    course_id = params[:course_id].to_i
    preference_key = "details_expanded"
    preference_value = params[:details_expanded]
    
    # Basic validation - ensure user can access this course
    course = Course.find(course_id)
    unless course.grants_any_right?(@current_user, :read)
      return render json: { error: 'Unauthorized' }, status: :unauthorized
    end
    
    @current_user.set_dashboard_card_preference(course_id, preference_key, preference_value)
    
    render json: { success: true }
  rescue => e
    render json: { error: e.message }, status: :unprocessable_entity
  end
end