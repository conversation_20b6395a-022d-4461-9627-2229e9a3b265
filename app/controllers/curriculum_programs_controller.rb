class CurriculumProgramsController < ApplicationController
    before_action :require_account_context
    before_action :get_department
    before_action :set_account
    before_action :set_department
    
    skip_before_action :verify_authenticity_token, only: [:batch_create, :delete_semester, :update_semester]
    
    def index
      curriculum_programs = @department.curriculum_programs.group_by(&:semester)
      @curriculum_programs = sort_curriculum_programs(curriculum_programs)
      add_crumb t("Curriculum")
    end
    
    def new
      @curriculum_program = @department.curriculum_programs.new
      add_crumb t("Departments"), account_departments_path(@account)
      add_crumb @department.name, account_department_path(@account, @department)
      add_crumb t("New Curriculum Program")
    end
    
    def create
      @curriculum_program = @department.curriculum_programs.new(curriculum_program_params)
      
      respond_to do |format|
        if @curriculum_program.save
          format.html do
            flash[:notice] = t("Curriculum program successfully created")
            redirect_to account_department_curriculum_programs_path(@account, @department)
          end
          format.json { render json: @curriculum_program }
        else
          format.html do
            add_crumb t("Departments"), account_departments_path(@account)
            add_crumb @department.name, account_department_path(@account, @department)
            add_crumb t("New Curriculum Program")
            render :new
          end
          format.json { render json: @curriculum_program.errors, status: :unprocessable_entity }
        end
      end
    end
    
    def edit
      @curriculum_program = @department.curriculum_programs.find(params[:id])
      add_crumb t("Departments"), account_departments_path(@account)
      add_crumb @department.name, account_department_path(@account, @department)
      add_crumb t("Edit Curriculum Program")
    end
    
    def update
      @curriculum_program = @department.curriculum_programs.find(params[:id])
      if @curriculum_program.update(curriculum_program_params)
        flash[:notice] = t("Curriculum program successfully updated")
        redirect_to account_department_curriculum_programs_path(@account, @department)
      else
        add_crumb t("Departments"), account_departments_path(@account)
        add_crumb @department.name, account_department_path(@account, @department)
        add_crumb t("Edit Curriculum Program")
        render :edit
      end
    end
    
    def destroy
      @curriculum_program = @department.curriculum_programs.find(params[:id])
      @curriculum_program.destroy
      flash[:notice] = t("Curriculum program successfully deleted")
      redirect_to account_department_curriculum_programs_path(@account, @department)
    end
    
    def batch_create
        semester = params[:semester]
        courses_params = params[:courses] || []
        
        created_courses = []
        
        begin
          ActiveRecord::Base.transaction do
            courses_params.each do |_index, course_data|
              course = @department.curriculum_programs.create!(
                semester: semester,
                course_code: course_data[:course_code],
                course_description: course_data[:course_description],
                prerequisite: course_data[:prerequisite],
                units: course_data[:units]
              )
              created_courses << course
            end
          end
          
          render json: { success: true, courses: created_courses }
        rescue => e
          Rails.logger.error("Error saving curriculum programs: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))
          render json: { success: false, error: e.message }, status: :unprocessable_entity
        end
      end
      def edit_semester
        @semester = params[:semester]
        @programs = @department.curriculum_programs.where(semester: @semester)
      end
      
      # Add this new action
      def update_semester
        old_semester = params[:old_semester]
        new_semester = params[:new_semester]
        courses_params = params[:courses] || []
        
        begin
          ActiveRecord::Base.transaction do
            # Update semester name if changed
            if old_semester != new_semester
              @department.curriculum_programs.where(semester: old_semester).update_all(semester: new_semester)
            end
            
            # Update existing courses
            existing_ids = []
            courses_params.each do |id, course_data|
              if id.to_s.match?(/^\d+$/) # If it's an existing record
                program = @department.curriculum_programs.find(id)
                program.update!(
                  course_code: course_data[:course_code],
                  course_description: course_data[:course_description],
                  prerequisite: course_data[:prerequisite],
                  units: course_data[:units]
                )
                existing_ids << program.id
              else # New course
                program = @department.curriculum_programs.create!(
                  semester: new_semester,
                  course_code: course_data[:course_code],
                  course_description: course_data[:course_description],
                  prerequisite: course_data[:prerequisite],
                  units: course_data[:units]
                )
                existing_ids << program.id
              end
            end
            
            # Delete removed courses
            @department.curriculum_programs.where(semester: new_semester).where.not(id: existing_ids).destroy_all
          end
          
          render json: { success: true }
        rescue => e
          Rails.logger.error("Error updating semester: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))
          render json: { success: false, error: e.message }, status: :unprocessable_entity
        end
      end
      def delete_semester
        semester = params[:semester]
        
        begin
          ActiveRecord::Base.transaction do
            @department.curriculum_programs.where(semester: semester).destroy_all
          end
          
          render json: { success: true }
        rescue => e
          Rails.logger.error("Error deleting semester: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))
          render json: { success: false, error: e.message }, status: :unprocessable_entity
        end
      end
    private
    def set_account
      @account = Account.find(params[:account_id])
    end
    
    def set_department
      @department = @account.departments.find(params[:department_id])
    end
    
    def delete_semester_params
      params.permit(:semester)
    end
    def get_department
      @department = @account.departments.find(params[:department_id])
    end
    
    def curriculum_program_params
      params.require(:curriculum_program).permit(:course_code, :course_description, :units, :prerequisite, :semester)
    end
    
    def require_account_management
      authorized_action(@account, @current_user, :manage_account_settings)
    end
    def sort_curriculum_programs(curriculum_programs)
      # Define the order for years and semesters
      year_order = ['1st Year', '2nd Year', '3rd Year', '4th Year', '5th Year']
      semester_order = ['1st Sem', '2nd Sem', '3rd Sem', 'Summer']
      
      # Sort the curriculum programs by the defined order
      sorted_programs = curriculum_programs.sort_by do |semester, programs|
        # Parse the semester string to extract year and semester parts
        parts = semester.split(' - ')
        year_part = parts[0]
        
        # Extract semester part (might have additional text after it)
        semester_part = parts[1]
        if semester_part
          # Check each semester option to see if it's contained in the semester_part
          matched_semester = semester_order.find { |sem| semester_part.include?(sem) }
          semester_part = matched_semester if matched_semester
        end
        
        # Get the index for sorting (default to 999 if not found to put unknowns at the end)
        year_index = year_order.index(year_part) || 999
        semester_index = semester_order.index(semester_part) || 999
        
        # Return array for sorting - first by year, then by semester
        [year_index, semester_index]
      end
      
      # Convert back to hash to maintain the grouped structure
      sorted_programs.to_h
    end
  end