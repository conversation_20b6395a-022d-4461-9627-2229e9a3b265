# frozen_string_literal: true

#
# Copyright (C) 2024 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTAB<PERSON>ITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

class Login::EmailVerificationController < ApplicationController
  include Login::Shared

  before_action :ensure_pending_token
  layout "bare"

  def ensure_pending_token
    # Find the pseudonym from the session
    pseudonym_id = session[:pending_email_token_pseudonym_id]
    @pseudonym = Pseudonym.find_by(id: pseudonym_id) if pseudonym_id

    # Redirect to login if no pending token in database
    unless @pseudonym&.pending_mfa?
      redirect_to login_url
    end
  end

  def new
    # Handle the case where we're coming from the login flow
    if session[:pending_email_token].present?
      # Get the pseudonym from the session
      pseudonym_id = session[:pending_email_token_pseudonym_id]
      @pseudonym = Pseudonym.find_by(id: pseudonym_id)
      
      # If the pseudonym is invalid, redirect to login
      unless @pseudonym
        flash[:error] = I18n.t("Email verification has expired. Please log in again.")
        redirect_to login_url
        return
      end
    elsif @current_user && @current_user.multi_factor_setting == User::MFA_TYPE_EMAIL
      # We're already logged in but need to verify email
      @pseudonym = @current_user.pseudonyms.active.first
      
      # If we don't have a pending MFA token, generate one
      unless @pseudonym.pending_mfa?
        token = @pseudonym.generate_mfa_token
        
        # Send the verification email
        begin
          @current_user.email_channel.send_otp!(token, @domain_root_account)
        rescue => e
          logger.error("Failed to send OTP email: #{e.class} - #{e.message}")
          flash[:error] = I18n.t("Failed to send verification email. Please try again.")
          redirect_to dashboard_url
          return
        end
      end
    else
      # No verification in progress
      flash[:error] = I18n.t("No email verification in progress.")
      redirect_to login_url
      return
    end
    
    @token_input = ""
    @user = @pseudonym.user
    render :new
  end

  def create
    entered_token = params[:token]&.strip

    if @pseudonym.verify_mfa_token(entered_token)
      # Set the verification flags to indicate email has been verified
      session[:email_token_verified] = true
      session[:email_token_verification] = true
      
      # Clear the pending token from the session
      session.delete(:pending_email_token)
      session.delete(:pending_email_token_pseudonym_id)

      # Determine if we're in the login flow or already logged in
      if @current_user
        # Already logged in, just redirect to dashboard
        flash[:notice] = t("Email verified successfully.")
        redirect_to dashboard_url
      else
        # Complete the login process
        session[:user_id] = @pseudonym.user.id
        session[:pseudonym_credentials_id] = @pseudonym.id
        @current_user = @pseudonym.user
        @current_pseudonym = @pseudonym

        # Log the successful login
        Auditors::Authentication.record(@pseudonym, "login")
        Canvas::LiveEvents.logged_in(session, @pseudonym.user, @pseudonym) if defined?(LiveEvents) && LiveEvents.respond_to?(:post_event)

        respond_to do |format|
          format.html { redirect_to dashboard_url(login_success: "1") }
          format.json { render json: { success: true }, status: :ok }
        end
      end
    else
      # Handle token verification failure
      if @pseudonym.mfa_token_expired? || @pseudonym.mfa_retry_count >= 5
        # Too many failed attempts or expired token - redirect to login page
        flash[:error] = t("Too many failed verification attempts or code has expired. Please log in again.")
        
        # Clear session data
        session.delete(:pending_email_token)
        session.delete(:pending_email_token_pseudonym_id)
        session.delete(:email_token_verified)
        session.delete(:email_token_verification)
        
        # Log the user out if they're logged in
        if @current_user
          logout_current_user
        end
        
        redirect_to login_canvas_url
        return
      else
        # Still has attempts remaining
        flash.now[:error] = t("Invalid verification code. Please try again. Attempts remaining: %{count}", count: 5 - @pseudonym.mfa_retry_count)
        @token_input = entered_token
        @user = @pseudonym.user
      end

      if params[:resend_token] && @pseudonym.user.email_channel
        # Generate a new token and send it
        token = @pseudonym.generate_mfa_token
        
        # Try to send the email with error handling
        begin
          @pseudonym.user.email_channel.send_otp!(token, @domain_root_account)
          flash.now[:notice] = t("A new verification code has been sent to your email.")
        rescue => e
          Rails.logger.error("Failed to resend OTP email: #{e.class} - #{e.message}")
          flash.now[:error] = t("We had trouble sending the verification code to your email. Please try again in a moment.")
        end
      end

      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: { success: false, error: "Invalid verification code" }, status: :unprocessable_entity }
      end
    end
  end
end
