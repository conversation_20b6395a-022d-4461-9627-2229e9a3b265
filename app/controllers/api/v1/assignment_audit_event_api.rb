# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

module Api::V1::AssignmentAuditEvent
  include Api::V1::Json
  include Api::V1::Assignment
  include Api::V1::User
  include Api::V1::Course

  def assignment_audit_events_compound_json(events, user, session)
    events_json = events.map { |event| assignment_audit_event_json(event, user, session) }

    assignments = extract_assignments(events)
    assignments_json = assignments.map { |assignment| assignment_json(assignment, user, session) }

    courses = extract_courses(events)
    courses_json = courses.map { |course| course_json(course, user, session, [], nil) }

    users = extract_users(events)
    users_json = users.map { |u| user_json(u, user, session) }

    {
      assignment_audit_events: events_json,
      linked: {
        assignments: assignments_json,
        courses: courses_json,
        users: users_json
      }
    }
  end

  def assignment_audit_event_json(event, user, session)
    json = api_json(event, user, session, only: %w[id created_at])
    json["event_type"] = event.event_type
    json["assignment_id"] = Shard.global_id_for(event.assignment_id)
    json["course_id"] = Shard.global_id_for(event.course_id)
    json["user_id"] = Shard.global_id_for(event.user_id)
    json
  end

  private

  def extract_assignments(events)
    assignment_ids = events.map(&:assignment_id).uniq
    Assignment.where(id: assignment_ids).to_a
  end

  def extract_courses(events)
    course_ids = events.map(&:course_id).uniq
    Course.where(id: course_ids).to_a
  end

  def extract_users(events)
    user_ids = events.map(&:user_id).uniq
    User.where(id: user_ids).to_a
  end
end
