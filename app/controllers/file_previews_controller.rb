# frozen_string_literal: true

require 'fileutils'
require 'tempfile'
require 'tmpdir'

# Document conversion service
require_dependency 'document_converter'

#
# Copyright (C) 2014 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

class FilePreviewsController < ApplicationController
  include AttachmentHelper

  before_action :get_context
  skip_before_action :get_context, only: [:converted_pdf, :converted_image]

  def token_auth_allowed?
    params[:action] == "show" && Account.site_admin.feature_enabled?(:rce_linked_file_urls)
  end

  # renders (or redirects to) appropriate content for the file, such as
  # canvadocs, crocodoc, inline image, etc.
  def show
    @file = @context.attachments.not_deleted.find_by(id: params[:file_id])
    css_bundle :react_files
    unless @file
      @headers = false
      @show_left_side = false
      return render template: "shared/errors/404_message",
                    status: :not_found,
                    formats: [:html]
    end

    if read_allowed(@file, @current_user, session, params)
      unless download_allowed(@file, @current_user, session, params)
        @lock_info = @file.locked_for?(@current_user)
        return render template: "file_previews/lock_explanation", layout: false
      end
      # mark item seen for module progression purposes
      @file.context_module_action(@current_user, :read) if @current_user
      log_asset_access(@file, "files", "files")
      # redirect to or render content for the file according to its type
      # crocodocs (if annotation requested) and canvadocs
      # images (including converted PDFs and office documents)
      if @file.content_type&.start_with?("image/") || @file.content_type == 'application/pdf' || office_document?(@file.content_type)
        handle_image_or_document_preview
      elsif (Canvas::Plugin.value_to_boolean(params[:annotate]) && (url = @file.crocodoc_url(@current_user))) ||
         (url = @file.canvadoc_url(@current_user))
        redirect_to url
      # google docs preview
      elsif GoogleDocsPreview.previewable?(@domain_root_account, @file)
        handle_google_docs_preview      
      # media files
      elsif %r{\A(audio|video)/}.match?(@file.content_type)
        js_env NEW_FILES_PREVIEW: 1
        js_bundle :file_preview
        render template: "file_previews/media_preview", layout: false
      # html files
      elsif @file.content_type == "text/html"
        redirect_to context_url(@context, :context_file_preview_url, @file.id)
      # CSV files
      elsif @file.content_type == 'text/csv' || @file.filename.end_with?('.csv')
        handle_csv_preview
      # no preview available
      else
        @accessed_asset = nil # otherwise it will double-log when they download the file
        render template: "file_previews/no_preview", layout: false
      end
    end
  end

  def read_allowed(attachment, user, session, params)
    if params[:verifier]
      verifier_checker = Attachments::Verification.new(attachment)
      return true if verifier_checker.valid_verifier_for_permission?(params[:verifier], :read, @domain_root_account, session)
    end
    jwt_resource_match(attachment) || authorized_action(attachment, user, :read)
  end

  def download_allowed(attachment, user, session, params)
    verifier_checker = Attachments::Verification.new(attachment)
    (params[:verifier] && verifier_checker.valid_verifier_for_permission?(params[:verifier], :download, @domain_root_account, session)) ||
      jwt_resource_match(attachment) ||
      attachment.grants_right?(user, session, :download)
  end

  def google_docs_proxy
    viewer_url = params[:viewer_url]
    return render plain: 'No viewer URL provided', status: :bad_request unless viewer_url.present?
    
    begin
      uri = URI.parse(viewer_url)
      
      response = Net::HTTP.start(uri.host, uri.port, use_ssl: uri.scheme == 'https') do |http|
        request = Net::HTTP::Get.new(uri)
        request['ngrok-skip-browser-warning'] = 'true'
        http.request(request)
      end
      
      render plain: response.body, status: response.code.to_i
    rescue URI::InvalidURIError => e
      Rails.logger.error("Invalid URI: #{e.message}")
      render plain: "Invalid URL: #{e.message}", status: :bad_request
    rescue StandardError => e
      Rails.logger.error("Google Docs proxy error: #{e.message}")
      render plain: "Error loading document: #{e.message}", status: :bad_gateway
    end
  end
  
  def temp_pdf
    file_path = CGI.unescape(params[:path].to_s)

    unless File.exist?(file_path) && file_path.to_s.downcase.end_with?('.pdf')
      render plain: 'File not found', status: :not_found
      return
    end

    # Set headers for inline PDF preview with CORS
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'inline'
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # Add CORS headers to allow cross-origin requests from the same domain
    response.headers['Access-Control-Allow-Origin'] = request.origin || '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept'

    # Handle CORS preflight requests
    if request.request_method == 'OPTIONS'
      head :ok
      return
    end

    # Stream the file with 8KB chunks for inline viewing
    send_file file_path,
              type: 'application/pdf',
              disposition: 'inline',
              stream: true,
              buffer_size: 8 * 1024,
              x_sendfile: false  # Ensure we're not using X-Sendfile which can interfere with CORS
  rescue => e
    Rails.logger.error("Error serving temporary PDF: #{e.message}")
    render plain: "Error serving PDF: #{e.message}", status: :internal_server_error
  end

  def converted_image
    token = params[:session_key]
    page = params[:page] || '1'

    Rails.logger.info("Converted image request - Token: #{token}, Page: #{page}")

    # Periodically clean up expired files (10% chance)
    cleanup_expired_converted_images if rand < 0.1

    # Check if token is provided
    unless token.present?
      Rails.logger.error("No token provided")
      render plain: 'Invalid request', status: :bad_request
      return
    end

    # Build file paths for multi-page structure
    cache_dir = Rails.root.join('tmp', 'converted_images')
    token_dir = File.join(cache_dir, token)
    metadata_path = File.join(token_dir, "metadata.json")

    # Check if token directory and metadata exist
    unless Dir.exist?(token_dir) && File.exist?(metadata_path)
      Rails.logger.error("Token directory or metadata not found - Dir: #{Dir.exist?(token_dir)}, Metadata: #{File.exist?(metadata_path)}")
      render plain: 'Image not found or expired', status: :not_found
      return
    end

    # Read metadata
    begin
      metadata = JSON.parse(File.read(metadata_path))
    rescue JSON::ParserError => e
      Rails.logger.error("Error parsing metadata: #{e.message}")
      render plain: 'Invalid metadata', status: :internal_server_error
      return
    end

    # Check if the images have expired (older than 1 hour)
    if Time.now.to_i - metadata['timestamp'] > 3600
      # Clean up expired files
      FileUtils.remove_entry(token_dir) if Dir.exist?(token_dir)
      Rails.logger.info("Cleaned up expired images: #{token}")
      render plain: 'Images have expired', status: :gone
      return
    end

    # Determine the specific page file
    page_filename = "page_%03d.png" % page.to_i
    cached_image_path = File.join(token_dir, page_filename)

    unless File.exist?(cached_image_path)
      Rails.logger.error("Specific page not found: #{page_filename}")
      render plain: 'Page not found', status: :not_found
      return
    end

    Rails.logger.info("Serving converted image page - Token: #{token}, Page: #{page}, File size: #{File.size(cached_image_path)}")

    # Set headers for image display
    response.headers['Content-Type'] = 'image/png'
    response.headers['Content-Disposition'] = 'inline'
    response.headers['Cache-Control'] = 'private, max-age=3600'
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # Send the image file directly
    send_file cached_image_path,
              type: 'image/png',
              disposition: 'inline',
              filename: "#{File.basename(metadata['filename'], '.*')}_page_#{page}.png",
              stream: true,
              buffer_size: 8 * 1024

  rescue => e
    Rails.logger.error("Error serving converted image: #{e.message}")
    Rails.logger.error("Backtrace: #{e.backtrace.join("\n")}")
    render plain: "Error serving image: #{e.message}", status: :internal_server_error
  end

  def converted_pdf
    token = params[:session_key]  # Keep the same parameter name for compatibility

    Rails.logger.info("Converted PDF request - Token: #{token}")

    # Periodically clean up expired files (10% chance)
    cleanup_expired_converted_pdfs if rand < 0.1

    # Check if token is provided
    unless token.present?
      Rails.logger.error("No token provided")
      render plain: 'Invalid request', status: :bad_request
      return
    end

    # Build file paths
    cache_dir = Rails.root.join('tmp', 'converted_pdfs')
    cached_pdf_path = File.join(cache_dir, "#{token}.pdf")
    metadata_path = File.join(cache_dir, "#{token}.json")

    # Check if files exist
    unless File.exist?(cached_pdf_path) && File.exist?(metadata_path)
      Rails.logger.error("PDF or metadata file not found - PDF: #{File.exist?(cached_pdf_path)}, Metadata: #{File.exist?(metadata_path)}")
      render plain: 'PDF not found or expired', status: :not_found
      return
    end

    # Read metadata
    begin
      metadata = JSON.parse(File.read(metadata_path))
    rescue JSON::ParserError => e
      Rails.logger.error("Error parsing metadata: #{e.message}")
      render plain: 'Invalid metadata', status: :internal_server_error
      return
    end

    # Check if the PDF has expired (older than 1 hour)
    if Time.now.to_i - metadata['timestamp'] > 3600
      # Clean up expired files
      File.delete(cached_pdf_path) if File.exist?(cached_pdf_path)
      File.delete(metadata_path) if File.exist?(metadata_path)
      Rails.logger.info("Cleaned up expired PDF: #{token}")
      render plain: 'PDF has expired', status: :gone
      return
    end

    Rails.logger.info("Serving converted PDF - Token: #{token}, File size: #{File.size(cached_pdf_path)}")

    # Set headers to mimic regular file downloads and avoid CORS issues
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'inline'
    response.headers['Cache-Control'] = 'private, max-age=3600'
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # Send the PDF file directly
    send_file cached_pdf_path,
              type: 'application/pdf',
              disposition: 'inline',
              filename: metadata['filename'],
              stream: true,
              buffer_size: 8 * 1024

  rescue => e
    Rails.logger.error("Error serving converted PDF: #{e.message}")
    Rails.logger.error("Backtrace: #{e.backtrace.join("\n")}")
    render plain: "Error serving PDF: #{e.message}", status: :internal_server_error
  end

  def handle_image_or_document_preview
    if @file.content_type&.start_with?("image/")
      # Regular image - use existing image preview
      render template: "file_previews/img_preview", layout: false
    elsif @file.content_type == 'application/pdf' || office_document?(@file.content_type)
      # PDF or office document - convert to image and display
      begin
        # Create a temporary directory for conversion
        temp_dir = Dir.mktmpdir
        temp_file = File.join(temp_dir, @file.filename)

        # Download the file content
        file_content = @file.open.read
        File.binwrite(temp_file, file_content)

        # Convert to images (multiple pages)
        Rails.logger.info("Converting file to images: #{temp_file}")
        image_paths = DocumentConverter.convert_to_image(temp_file, temp_dir, 'png')
        Rails.logger.info("Conversion result: #{image_paths.length} images created")

        if image_paths && image_paths.any? { |path| File.exist?(path) }
          # Create a unique token for this converted document
          token = SecureRandom.hex(32)

          # Create a cache directory for converted images
          cache_dir = Rails.root.join('tmp', 'converted_images')
          token_dir = File.join(cache_dir, token)
          FileUtils.mkdir_p(token_dir) unless Dir.exist?(token_dir)

          # Move all converted images to the cache directory
          cached_image_paths = []
          image_paths.each_with_index do |image_path, index|
            if File.exist?(image_path)
              page_filename = "page_%03d.png" % (index + 1)
              cached_image_path = File.join(token_dir, page_filename)
              FileUtils.mv(image_path, cached_image_path)
              cached_image_paths << cached_image_path
              Rails.logger.info("Cached page #{index + 1}: #{cached_image_path}")
            end
          end

          # Create a metadata file alongside the images
          metadata = {
            filename: "#{File.basename(@file.filename, '.*')}.png",
            timestamp: Time.now.to_i,
            original_file_id: @file.id,
            user_id: @current_user&.id,
            page_count: cached_image_paths.length,
            pages: cached_image_paths.map { |path| File.basename(path) }
          }
          metadata_path = File.join(token_dir, "metadata.json")
          File.write(metadata_path, metadata.to_json)

          Rails.logger.info("Stored converted images with token - Token: #{token}, Pages: #{cached_image_paths.length}")

          # Set variables for the multi-page template
          @document_token = token
          @page_count = cached_image_paths.length
          @base_image_url = converted_image_preview_path(session_key: token, page: 'PAGE_NUM')
          Rails.logger.info("Generated multi-page document - Token: #{@document_token}, Pages: #{@page_count}")
          render template: 'file_previews/multi_page_preview', layout: false
          return
        else
          raise "Failed to convert document to images"
        end

      rescue DocumentConverter::ConversionError => e
        Rails.logger.error("Failed to convert document: #{e.message}")
        render plain: "Failed to generate image preview: #{e.message}", status: :internal_server_error
      rescue => e
        Rails.logger.error("Error in document preview: #{e.message}\n#{e.backtrace.join("\n")}")
        render plain: "Error generating preview: #{e.message}", status: :internal_server_error
      ensure
        # Clean up temporary files immediately since we've stored the data in cache
        if temp_dir && Dir.exist?(temp_dir)
          FileUtils.remove_entry(temp_dir) rescue nil
        end
      end
    end
  end

  def handle_google_docs_preview
    if @file.content_type == 'application/pdf'
      # For PDFs, use the authenticated URL
      @pdf_url = @file.authenticated_s3_url
      render template: 'file_previews/pdf_preview', layout: false
    elsif office_document?(@file.content_type)
      begin
        # Create a temporary directory for conversion
        temp_dir = Dir.mktmpdir
        temp_file = File.join(temp_dir, @file.filename)

        # Download the file content
        file_content = @file.open.read
        File.binwrite(temp_file, file_content)

        # Convert to PDF
        Rails.logger.info("Converting file to PDF: #{temp_file}")
        pdf_path = DocumentConverter.convert_to_pdf(temp_file, temp_dir)
        Rails.logger.info("Conversion result: #{pdf_path}")

        if pdf_path && File.exist?(pdf_path)
          # Create a unique token for this converted PDF
          token = SecureRandom.hex(32)

          # Create a cache directory for converted PDFs
          cache_dir = Rails.root.join('tmp', 'converted_pdfs')
          FileUtils.mkdir_p(cache_dir) unless Dir.exist?(cache_dir)

          # Move the converted PDF to the cache directory with the token as filename
          cached_pdf_path = File.join(cache_dir, "#{token}.pdf")
          FileUtils.mv(pdf_path, cached_pdf_path)

          # Create a metadata file alongside the PDF
          metadata = {
            filename: "#{File.basename(@file.filename, '.*')}.pdf",
            timestamp: Time.now.to_i,
            original_file_id: @file.id,
            user_id: @current_user&.id
          }
          metadata_path = File.join(cache_dir, "#{token}.json")
          File.write(metadata_path, metadata.to_json)

          Rails.logger.info("Stored converted PDF with token - Token: #{token}, Path: #{cached_pdf_path}")
          Rails.logger.info("File exists: #{File.exist?(cached_pdf_path)}, File size: #{File.size(cached_pdf_path) if File.exist?(cached_pdf_path)}")

          # Use a URL that mimics the regular file download pattern
          # This helps avoid CORS issues with PDF.js
          @pdf_url = converted_pdf_preview_path(session_key: token)
          Rails.logger.info("Generated PDF URL: #{@pdf_url}")
          render template: 'file_previews/pdf_preview', layout: false
          return
        else
          raise "Failed to convert document to PDF"
        end

      rescue DocumentConverter::ConversionError => e
        Rails.logger.error("Failed to convert document: #{e.message}")
        render plain: "Failed to generate PDF preview: #{e.message}", status: :internal_server_error
      rescue => e
        Rails.logger.error("Error in document preview: #{e.message}\n#{e.backtrace.join("\n")}")
        render plain: "Error generating preview: #{e.message}", status: :internal_server_error
      ensure
        # Clean up temporary files immediately since we've stored the data in session
        if temp_dir && Dir.exist?(temp_dir)
          FileUtils.remove_entry(temp_dir) rescue nil
        end
      end
    else
      redirect_to("//docs.google.com/viewer?" + { embedded: true, url: }.to_query)
    end
  end

  private

  def handle_csv_preview
    # For CSV files, read and parse the content
    require 'csv'
    begin
      @csv_content = CSV.read(@file.full_filename)
      render template: 'file_previews/csv_preview', layout: false
    rescue => e
      Rails.logger.error("CSV Preview Error: #{e.message}")
      redirect_to @file.public_url
    end
  end
  
  def office_document?(content_type)
    # Common Office document MIME types
    office_mime_types = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.oasis.opendocument.text',
      'application/vnd.oasis.opendocument.spreadsheet',
      'application/vnd.oasis.opendocument.presentation',
      'application/rtf',
      'text/rtf',
      'text/plain'
    ]
    office_mime_types.include?(content_type)
  end
  
  def serve_pdf_preview(file_path, original_filename)
    # Create a unique filename for the converted PDF
    pdf_filename = "#{File.basename(original_filename, '.*')}.pdf"

    # Set response headers for PDF
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = "inline; filename=#{pdf_filename}"

    # Stream the file
    send_file file_path,
              filename: pdf_filename,
              type: 'application/pdf',
              disposition: 'inline'
  rescue => e
    Rails.logger.error("Error serving PDF preview: #{e.message}")
    render plain: "Error generating preview: #{e.message}", status: :internal_server_error
  end

  def cleanup_expired_converted_pdfs
    # Clean up converted PDFs older than 2 hours
    cache_dir = Rails.root.join('tmp', 'converted_pdfs')
    return unless Dir.exist?(cache_dir)

    cutoff_time = 2.hours.ago

    # Clean up PDF files
    Dir.glob(File.join(cache_dir, '*.pdf')).each do |pdf_path|
      if File.mtime(pdf_path) < cutoff_time
        token = File.basename(pdf_path, '.pdf')
        metadata_path = File.join(cache_dir, "#{token}.json")

        File.delete(pdf_path) rescue nil
        File.delete(metadata_path) if File.exist?(metadata_path) rescue nil
        Rails.logger.info("Cleaned up expired converted PDF: #{token}")
      end
    end

    # Clean up orphaned metadata files
    Dir.glob(File.join(cache_dir, '*.json')).each do |metadata_path|
      if File.mtime(metadata_path) < cutoff_time
        token = File.basename(metadata_path, '.json')
        pdf_path = File.join(cache_dir, "#{token}.pdf")

        File.delete(metadata_path) rescue nil
        File.delete(pdf_path) if File.exist?(pdf_path) rescue nil
        Rails.logger.info("Cleaned up orphaned metadata: #{token}")
      end
    end
  end

  def cleanup_expired_converted_images
    # Clean up converted images older than 2 hours
    cache_dir = Rails.root.join('tmp', 'converted_images')
    return unless Dir.exist?(cache_dir)

    cutoff_time = 2.hours.ago

    # Clean up token directories (new multi-page structure)
    Dir.glob(File.join(cache_dir, '*')).each do |token_path|
      next unless Dir.exist?(token_path)

      token = File.basename(token_path)
      metadata_path = File.join(token_path, 'metadata.json')

      # Check if directory is expired based on metadata or directory modification time
      should_cleanup = false

      if File.exist?(metadata_path)
        begin
          metadata = JSON.parse(File.read(metadata_path))
          should_cleanup = (Time.now.to_i - metadata['timestamp']) > 7200 # 2 hours
        rescue
          # If metadata is corrupted, clean up based on file time
          should_cleanup = File.mtime(metadata_path) < cutoff_time
        end
      else
        # No metadata, clean up based on directory modification time
        should_cleanup = File.mtime(token_path) < cutoff_time
      end

      if should_cleanup
        FileUtils.remove_entry(token_path) rescue nil
        Rails.logger.info("Cleaned up expired converted images directory: #{token}")
      end
    end

    # Also clean up any old single-file images (legacy cleanup)
    Dir.glob(File.join(cache_dir, '*.png')).each do |image_path|
      if File.mtime(image_path) < cutoff_time
        token = File.basename(image_path, '.png')
        metadata_path = File.join(cache_dir, "#{token}.json")

        File.delete(image_path) rescue nil
        File.delete(metadata_path) if File.exist?(metadata_path) rescue nil
        Rails.logger.info("Cleaned up legacy converted image: #{token}")
      end
    end

    # Clean up orphaned legacy metadata files
    Dir.glob(File.join(cache_dir, '*.json')).each do |metadata_path|
      if File.mtime(metadata_path) < cutoff_time
        token = File.basename(metadata_path, '.json')
        image_path = File.join(cache_dir, "#{token}.png")

        File.delete(metadata_path) rescue nil
        File.delete(image_path) if File.exist?(image_path) rescue nil
        Rails.logger.info("Cleaned up legacy orphaned metadata: #{token}")
      end
    end
  end
end
