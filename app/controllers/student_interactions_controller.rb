class StudentInteractionsController < ApplicationController
  before_action :require_user
  before_action :get_course
  
  def index
    if authorized_action(@course, @current_user, [:read_as_admin, :manage_grades, :view_all_grades])
      @teacher = @current_user
      @courses = {}
      
      # Get the current course and its students - matching the existing template expectations
      if @course.grants_right?(@current_user, :read_roster)
        students_data = []
        
        # Get all active student enrollments for this course
        student_enrollments = @course.student_enrollments.active
                                    .preload(:user, :scores)
                                    .joins(:user)
                                    .order('users.sortable_name')
        
        student_enrollments.each do |enrollment|
          student_info = { enrollment: enrollment }
          
          # Get last interaction - check both PageViews and AssetUserAccess
          last_page_view = PageView.where(
            user_id: enrollment.user_id,
            context_type: 'Course',
            context_id: @course.id
          ).maximum(:created_at)
          
          last_asset_access = AssetUserAccess.where(
            user_id: enrollment.user_id,
            context_type: 'Course', 
            context_id: @course.id
          ).maximum(:last_access)
          
          # Use the most recent of the two
          student_info[:last_interaction] = [last_page_view, last_asset_access].compact.max
          
          # Get ungraded submissions - using course's assignments directly
          course_assignment_ids = @course.assignments.published.pluck(:id)
          ungraded_submissions = Submission.where(
            user_id: enrollment.user_id,
            assignment_id: course_assignment_ids,
            workflow_state: ['submitted', 'pending_review']
          ).where(
            'submissions.score IS NULL AND submissions.grade IS NULL'
          ).preload(:assignment)
          .joins(:assignment)
          .order('assignments.due_at ASC NULLS LAST, assignments.created_at ASC')
          
          student_info[:ungraded] = ungraded_submissions.to_a
          
          # Add access report data for each student
          student_info[:access_report] = get_student_access_report(enrollment.user)
          
          students_data << student_info
        end
        
        # Sort students by last interaction (most recent first, then by name)
        students_data.sort! do |a, b|
          if a[:last_interaction] && b[:last_interaction]
            b[:last_interaction] <=> a[:last_interaction]
          elsif a[:last_interaction]
            -1
          elsif b[:last_interaction]
            1
          else
            a[:enrollment].user.sortable_name <=> b[:enrollment].user.sortable_name
          end
        end
        
        @courses[@course] = students_data
      else
        @courses[@course] = []
      end
      
      # Render the existing teacher activity template
      render template: 'users/teacher_activity'
    end
  end

  # New action to show detailed access report for a specific student
  def show_access_report
    return unless authorized_action(@course, @current_user, [:read_as_admin, :manage_grades, :view_all_grades])
    return unless @course.grants_right?(@current_user, :read_roster)
    
    @user = @course.users.find(params[:user_id])
    
    # Get asset user access data for this student in this course
    @accesses = AssetUserAccess.where(
      user_id: @user.id,
      context_type: 'Course',
      context_id: @course.id
    ).order('last_access DESC NULLS LAST')
    .page(params[:page])
    .per(50)
    
    # Get the user's last activity in the course
    @last_activity_at = [@user.last_activity_at, 
                         PageView.where(user_id: @user.id, context_type: 'Course', context_id: @course.id).maximum(:created_at)
                        ].compact.max
    
    # Set expiration date for old data (typically 1 year)
    @aua_expiration_date = 1.year.ago
    
    render template: 'users/user_access_report'
  end
  
  private
  
  def get_course
    @course = Course.find(params[:course_id])
    @context = @course
  end
  
  def get_student_access_report(user)
    # Get recent asset access data for quick summary
    recent_accesses = AssetUserAccess.where(
      user_id: user.id,
      context_type: 'Course',
      context_id: @course.id
    ).where('last_access > ?', 30.days.ago)
    .order('last_access DESC')
    .limit(10)
    
    # Calculate summary statistics
    total_views = recent_accesses.sum(:view_score)
    total_participations = recent_accesses.sum(:participate_score)
    unique_items_accessed = recent_accesses.count
    
    # Get most recent access
    last_access = recent_accesses.maximum(:last_access)
    
    {
      total_views: total_views,
      total_participations: total_participations,
      unique_items_accessed: unique_items_accessed,
      last_access: last_access,
      recent_accesses: recent_accesses.to_a
    }
  end
end