# Update your existing DepartmentsController to handle API requests

class DepartmentsController < ApplicationController
  before_action :require_account_context
  before_action :set_account
  before_action :require_account_management, except: [:index, :show]
  
  def index
    @departments = @account.departments
    
    respond_to do |format|
      format.html { add_crumb t("Departments") }
      format.json do
        departments_data = @departments.select(:id, :name, :code).order(:name).map do |dept|
          {
            id: dept.id,
            name: dept.name,
            code: dept.code
          }
        end
        render json: departments_data
      end
    end
  end
  
  # ... rest of your existing methods remain the same ...
  
  def show
    @department = @account.departments.find(params[:id])
    redirect_to account_department_curriculum_programs_path(@account, @department)
  end

  def new
    @department = @account.departments.new
    add_crumb t("Departments"), account_departments_path(@account)
    add_crumb t("New Department")
  end

  def create
    @department = @account.departments.new(department_params)
    
    respond_to do |format|
      if @department.save
        # Process semester and course data if present
        if params[:semesters].present?
          params[:semesters].each do |_, semester_data|
            semester_name = semester_data[:name]
            
            if semester_data[:courses].present?
              semester_data[:courses].each do |_, course_data|
                @department.curriculum_programs.create(
                  semester: semester_name,
                  course_code: course_data[:code],
                  course_description: course_data[:name],
                  prerequisite: course_data[:prerequisite],
                  units: course_data[:units]
                )
              end
            end
          end
        end
        
        format.html { 
          flash[:notice] = t("Department successfully created")
          redirect_to account_department_curriculum_programs_path(@account, @department)
        }
        format.json { render json: { success: true, redirect_url: account_department_curriculum_programs_path(@account, @department) } }
      else
        format.html {
          add_crumb t("Departments"), account_departments_path(@account)
          add_crumb t("New Department")
          render :new
        }
        format.json { render json: { success: false, errors: @department.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  def edit
    @department = @account.departments.find(params[:id])
    add_crumb t("Departments"), account_departments_path(@account)
    add_crumb @department.name, account_department_path(@account, @department)
    add_crumb t("Edit")
  end

  def update
    @department = @account.departments.find(params[:id])
    if @department.update(department_params)
      flash[:notice] = t("Department successfully updated")
      redirect_to account_department_path(@account, @department)
    else
      add_crumb t("Departments"), account_departments_path(@account)
      add_crumb @department.name, account_department_path(@account, @department)
      add_crumb t("Edit")
      render :edit
    end
  end

  def destroy
    @department = @account.departments.find(params[:id])
    @department.destroy
    flash[:notice] = t("Department successfully deleted")
    redirect_to account_departments_path(@account)
  end

  def curriculum_programs_by_id
    department_id = params[:department_id]
    puts "Looking for department with ID: #{department_id}"
    
    department = @account.departments.find_by(id: department_id)
    puts "Found department: #{department&.name}"
    
    if department
      curriculum_programs = department.curriculum_programs
                                    .select(:course_code, :course_description, :units, :prerequisite)
                                    .order(:course_code)
      
      puts "Found #{department.curriculum_programs.count} curriculum programs"
      curriculum_programs.each { |p| puts "- #{p.course_code}: #{p.course_description}" }
      
      render json: { 
        success: true, 
        curriculum_programs: curriculum_programs 
      }
    else
      puts "Department not found"
      render json: { 
        success: false, 
        curriculum_programs: [] 
      }
    end
  end

  private
  
  def set_account
    @account = Account.find(params[:account_id])
  end
  
  def require_account_management
    authorized_action(@account, @current_user, :manage_account_settings)
  end
  
  def department_params
    params.require(:department).permit(:name, :code)
  end
end