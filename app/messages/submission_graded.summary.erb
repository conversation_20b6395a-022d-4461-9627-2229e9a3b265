<% define_content :link do %>
  <%= polymorphic_url([asset.assignment.context, asset.assignment]) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Assignment Graded: %{assignment}, %{context}", :assignment => asset.assignment.title, :context => asset.assignment.context.name %>
<% end %>

<% if send_student_names(asset, user) %>
  <%= t "For %{name}", name: asset.user.name %>
<% end %>
<%= t :graded_date, "graded: %{date}", :date => (datetime_string(force_zone(asset.graded_at)) rescue t(:no_date_set, "No Date Set")) %>
<% if asset.score && user.try(:send_scores_in_emails?, asset.assignment.context) %>
  <% if asset.assignment.restrict_quantitative_data?(user)%>
    <%=t :grade, "grade: %{letter_grade}", :letter_grade => asset.assignment.score_to_grade(asset.score, asset.grade, true)%>
  <% else %>
    <%= t :score, "score: %{score} out of %{total}", :score => asset.score, :total => (asset.assignment.points_possible || t(:not_applicable, "N/A")) %>
  <% end %>
<% end %>
<%= t(:score_pending_review, "score pending review by the teacher") if asset.pending_review? %>
