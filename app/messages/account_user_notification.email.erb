<% define_content :link do %>
  <%= account_url(asset.account_id) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Account Admin Notification" %>
<% end %>

<%= t("You've been given an account role for the %{account} account at %{website}. Role type: %{role_type}.",
    :role_type => asset.readable_type, :account => asset.account.name, :website => HostUrl.context_host(asset.account)) %>

<%= t(:url_info, "Visit the account page here:\n%{url}", :url => content(:link)) %>
