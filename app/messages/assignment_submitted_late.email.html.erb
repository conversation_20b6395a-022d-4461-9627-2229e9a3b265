<% presenter = ::Messages::AssignmentSubmittedLate::EmailPresenter.new(self) %>

<% define_content :link do %>
  <%= presenter.link.html_safe %>
<% end %>

<% define_content :subject do %>
  <%= presenter.subject %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "Click here to view the submission" %>
  </a>
<% end %>

<p>
  <%= presenter.body %>
</p>
