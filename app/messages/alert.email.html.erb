<% define_content :link do %>
  <%= course_url data.course_id %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'Alert: %{user_name}', :user_name => data.student_name) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t('view_here', 'You can view it here') %>
  </a>
<% end %>

<p>
<%=
    if data.user_id == user.id
      t('body_student', {:one => "An alert has been triggered for you because the following condition has been met:", :other => "An alert has been triggered for you because the following conditions have been met:"}, :count => asset.criteria.length)
    else
      t('body', {:one => "An alert has been triggered for %{student} because the following condition has been met:", :other => "An alert has been triggered for %{student} because the following conditions have been met:"}, :count => asset.criteria.length, :student => data.student_name)
    end
%>
</p>

<% asset.criteria.each do |criterion| %>
  <p><em style="color: #ff0000;"><%=
      case criterion.criterion_type
        when 'Interaction'
          t('interaction_description', 'No student/teacher interaction for %{count} days', :count => n(criterion.threshold))
        when 'UngradedCount'
          t('ungraded_count_description', '%{count} or more submissions have not been graded', :count => n(criterion.threshold))
        when 'UngradedTimespan'
          t('ungraded_timespan_description', 'A submission has been left ungraded for %{count} days', :count => n(criterion.threshold))
      end
  %></em></p>
<% end %>
