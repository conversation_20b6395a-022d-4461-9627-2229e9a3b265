<% define_content :link do %>
  <% link = if asset.is_a? WebZipExport %>
      <% course_offline_web_exports_url(asset.course) %>
    <% elsif asset.is_a? EpubExport %>
      <% epub_exports_url %>
    <% else %>
      <% polymorphic_url([asset.context, :content_exports]) %>
    <% end %>
  <%= link %>
<% end %>

<% define_content :subject do %>
<%= t :subject, "Course Export Failed: %{course}", :course => asset.context.name %>
<% end %>

<%= t :body, <<-BODY, :course => asset.context.name, :id => %{"#{asset.class}:#{asset.id}"}
There was a problem exporting the course "%{course}".
Please notify your system administrator, and give them the following export identifier: %{id}.
BODY
%>

<%= content :link %>
