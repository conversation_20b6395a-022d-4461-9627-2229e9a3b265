<% define_content :link do %>
  <%= course_url(asset.course) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Course Enrollment" %>
<% end %>

<%= t "You've been enrolled in the course, %{course}. Course role: %{role_type}", :course => asset.course.name, :role_type => asset.readable_role_name %>
<% email = asset.user.email; login = (asset.user.pseudonym.unique_id rescue "none") %>
<%= before_label :name, "Name" %> <%= asset.user.name %>
<%= before_label :email, "Email" %> <%= asset.user.email %>
<% if email != login %><%= before_label :username, "Username" %> <%= asset.user.pseudonym.unique_id rescue t(:none, "none") %><% end %>

<% if !asset.user.registered? && asset.user.communication_channel %>
<%= t :register, "Visit %{link} to complete registration", :link => registration_confirmation_url(asset.user.communication_channel.confirmation_code, :host => HostUrl.context_host(asset.course)) %>
<% end %>

<%= t :details, "Visit the course page here:" %>
<%= content :link %>
