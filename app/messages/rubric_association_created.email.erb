<% define_content :link do %>
  <%= polymorphic_url([asset.context, :rubric], id: asset.rubric_id) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "New Assessment: %{title}, %{context}", :title => asset.title, :context => asset.context.name %>
<% end %>

<%= t :body, "A new assessment has been created for %{context}:", :context => asset.context.name %>

<%= asset.title %>

<%= t :link_message, "You can review the assessment and submit your entry here:" %>
<%= content :link %>
