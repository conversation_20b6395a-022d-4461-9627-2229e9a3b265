<% define_content :link do %>
  <%= polymorphic_url([asset.submission.assignment.context, asset.submission.assignment, :submission], id: asset.submission.user) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Submission Comment: %{user}, %{assignment}, %{context}",
        :user => submission_comment_submittor(asset, user),
        :assignment => asset.submission.assignment.title,
        :context => asset.submission.assignment.context.name %>
<% end %>

<%= t :body, "%{author} just made a new comment on the submission for %{user} for %{assignment}.",
      :author => submission_comment_author(asset, user),
      :user => submission_comment_submittor(asset, user),
      :assignment => asset.submission.assignment.title %>
