<% define_content :link do %>
  <%= polymorphic_url([asset.group.context, asset.group]) %>
<% end %>
<% define_content :subject do %>
    <%= t :subject, "New Group Membership for %{course_or_account}", :course_or_account => asset.group.context.name %>
<% end %>

<%=
    case asset.group.context_type
    when 'Account'
      t :body_account, "You've been invited to join the group %{group} for the account %{account}.", :group => asset.group.name, :account => asset.group.context.name
    else
      t :body_course, "You've been invited to join the group %{group} for the course %{course}.", :group => asset.group.name, :course => asset.group.context.name
    end
%>
