<% define_content :link do %>
  <%= registration_confirmation_url(asset.user.communication_channel.confirmation_code) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "<PERSON><PERSON> Account Admin Invitation" %>
<% end %>

<%= t("You've been given an account role for the %{account} account at %{website}. Role type: %{role_type}.",
    :role_type => asset.readable_type, :account => asset.account.name, :website => HostUrl.context_host(asset.account)) %>

<% email = asset.user.email; login = (asset.user.pseudonym.unique_id rescue "none") %>
<%= t(:name_info, "Name: %{name}", :name => asset.user.name) %>
<%= t(:email_info, "Email: %{email}", :email => email) %>
<% if email != login %><%= t(:username_info, "Username: %{username}", :username => login) %><% end %>

<%= t(:url_info, "You'll need to register with Canvas before you can participate as an account admin.  You can finish the registration process here:\n%{url}", :url => content(:link)) %>
