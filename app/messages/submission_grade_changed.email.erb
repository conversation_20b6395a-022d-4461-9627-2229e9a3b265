<% # evizitei: this template is used for submissions AND quiz_submissions, make sure methods used are available on both models %>

<% define_content :link do %>
  <% if asset.assignment.context.feature_enabled?(:assignments_2_student) %>
    <%= course_assignment_url(asset.assignment.context, asset.assignment) %>
  <% else %>
    <%= course_assignment_submission_url(asset.assignment.context, asset.assignment, asset.user_id) %>
  <% end %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Grade Changed: %{assignment}, %{context}", :assignment => asset.assignment.title, :context => asset.assignment.context.name %>
<% end %>

<% if send_student_names(asset, user) %>
<%= t "The grade on assignment %{assignment_title} has been changed for %{name}.", :assignment_title => asset.assignment.title, name: asset.user.name %>
<% else %>
<%= t :body, "The grade on your assignment %{assignment_title} has been changed.", :assignment_title => asset.assignment.title %>
<% end %>

<%= t :regraded_date, "re-graded: %{date}", :date => (datetime_string(force_zone(asset.graded_at)) rescue t(:no_date_set, "No Date Set")) %>
<% if user.try(:send_scores_in_emails?, asset.assignment.context) %>
  <% if asset.excused? %>
    <%= t :excused, "This assignment has been excused." %>
  <% elsif asset.score %>
    <% if asset.assignment.restrict_quantitative_data?(user)%>
      <%=t :grade, "grade: %{letter_grade}", :letter_grade => asset.assignment.score_to_grade(asset.score, asset.grade, true)%>
    <% else %>
      <%= t :score, "score: %{score} out of %{total}", :score => asset.score, :total => (asset.assignment.points_possible || t(:not_applicable, "N/A")) %>
    <% end %>
  <% end %>
<% end %>

<%= t(:score_pending, "score pending review by the teacher") if asset.pending_review? %>

<%= t :link_message, "You can review the assignment here:" %>
<%= content :link %>
