<% define_content :link do %>
  <%= appointment_group_url(asset.appointment_group, :event_id => asset.to_param) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'You have been signed up for "%{appointment_name}"', :appointment_name => asset.title) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_text, 'Sign up for a different time slot' %>
  </a>
<% end %>

<p><%= t :message, '%{user} has signed you up for "%{appointment_name}".', :user => data.updating_user_name || data.updating_user.name, :appointment_name => asset.title %></p>

<p><strong><%= t :details, 'Appointment Details' %>:</strong></p>
<table border="0" style="font-size: 14px; color: #444444;
    font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, 'Lucida Sans Unicode', <PERSON><PERSON><PERSON>, 'Sans Serif';
    border-collapse: collapse;">
    <tr>
        <td style="padding-right: 10px;" valign="top"><%= t :date_and_time, 'Date/time' %>:</td>
        <td valign="top"><%= datetime_string(asset.start_at, asset.end_at) %></td>
    </tr>
    <% if asset.appointment_group.participant_type == 'Group' -%>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :attendees, 'Attendees' %>:</td>
          <td valign="top"><%= asset.participants.map(&:name).to_sentence %></td>
      </tr>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :group, 'Group' %>:</td>
          <td valign="top"><%= asset.context.name %></td>
      </tr>
    <% else %>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :attendee, 'Attendee' %>:</td>
          <td valign="top"><%= asset.context.name %></td>
      </tr>
    <% end %>
    <tr>
        <td style="padding-right: 10px;" valign="top"><%= t :course, 'Course' %>:</td>
        <td valign="top"><%= asset.appointment_group.participant_type == 'Group' ?
                                 asset.appointment_group.contexts.first.name  :
                                 asset.appointment_group.contexts_for_user(user).map(&:name).join(", ") %></td>
    </tr>
    <% if asset.appointment_group.available_slots -%>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :slots_remaining, 'Available time slots' %>:</td>
          <td valign="top"><%= asset.appointment_group.available_slots(current_only: true) %></td>
      </tr>
    <% end %>
</table>
