<% define_content :link do %>
  <%= appointment_group_url(asset.appointment_group) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_text, 'Click here to sign up for a different time slot' %>
  </a>
<% end %>

<% courses = asset.appointment_group.participant_type == 'Group' ?
    asset.appointment_group.contexts.first.name  :
    asset.appointment_group.contexts_for_user(user).map(&:name).join(", ") %>
<% define_content :subject do %>
  <%= t('subject', 'Your time slot for %{appointment_name} has been canceled (%{course})', :appointment_name => asset.title, :course => courses) %>
<% end %>

<p><%= t :message, '%{user} canceled your time slot for %{appointment_name}.', :user => data.updating_user_name || data.updating_user.name, :appointment_name => asset.title %></p>

<strong><%= t :details, 'Appointment Details' %>:</strong>
<p>
<table border="0" style="font-size: 14px; color: #444444;
    font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';
    border-collapse: collapse;">
    <tr>
        <td style="padding-right: 10px;" valign="top"><%= t :date_and_time, 'Date/Time' %>:</td>
        <td valign="top"><%= datetime_string(asset.start_at, asset.end_at) %></td>
    </tr>
    <% if asset.appointment_group.participant_type == 'Group' -%>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :attendees, 'Attendees' %>:</td>
          <td valign="top"><%= asset.participants.map(&:name).to_sentence %></td>
      </tr>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%=  t :group, 'Group' %>:</td>
          <td valign="top"><%= asset.context.name %></td>
      </tr>
    <% else -%>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :attendee, 'Attendee' %>:</td>
          <td valign="top"><%= asset.context.name %></td>
      </tr>
    <% end %>
    <tr>
        <td style="padding-right: 10px;" valign="top"><%= t :course, 'Course' %>:</td>
        <td valign="top"><%= courses %></td>
    </tr>
</table>
<p style="font-weight: bold;"><%= t :cancel_reason, 'Reason for canceling' %></p>
<p><%= data.cancel_reason || t(:no_reason_given, "none given") %></p>
