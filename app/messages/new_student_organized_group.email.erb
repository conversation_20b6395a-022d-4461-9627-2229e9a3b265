<% define_content :link do %>
  <%= polymorphic_url([asset.group.context, asset.group]) %>
<% end %>
<% define_content :subject do %>
  <%= t :subject, "New Student Group for %{course_or_account}", :course_or_account => asset.group.context.name %>
<% end %>

<%= t :title, "New Student Group: %{group_name}", :group_name => asset.group.name %>

<%=
    case asset.group.context_type
    when 'Account'
      t :body_account, %{A new student organized group named "%{group_name}" was created for the account %{account}. The first member is %{user}.}, :group_name => asset.group.name, :account => asset.group.context.name, :user => asset.user.name
    else
      t :body_course, %{A new student organized group named "%{group_name}" was created for the course %{course}. The first member is %{user}.}, :group_name => asset.group.name, :course => asset.group.context.name, :user => asset.user.name
    end
%>

<%= t("You can see details for this group by clicking this link: %{link}.", link: content(:link)) %>
