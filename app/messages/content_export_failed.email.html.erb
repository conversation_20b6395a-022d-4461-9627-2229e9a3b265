<% define_content :link do %>
  <% link = if asset.is_a? WebZipExport %>
      <% course_offline_web_exports_url(asset.course) %>
    <% elsif asset.is_a? EpubExport %>
      <% epub_exports_url %>
    <% else %>
      <% polymorphic_url([asset.context, :content_exports]) %>
    <% end %>
  <%= link %>
<% end %>

<% define_content :subject do %>
<%= t :subject, "Course Export Failed: %{course}", :course => asset.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "Click to view exports" %>
  </a>
<% end %>

<p>
<%= t :body2, <<-BODY, :course => asset.context.name, :wrapper => '<em>\1</em>'
There was a problem exporting the course *%{course}*.
Please notify your system administrator, and give them the following export identifier:
BODY
%>
</p>

<p><strong><%= asset.class %>:<%= asset.id %></strong></p>
