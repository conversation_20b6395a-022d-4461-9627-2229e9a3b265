<% define_content :link do %>
  <% if asset.submission.assignment.context.feature_enabled?(:assignments_2_student) %>
    <%= polymorphic_url([asset.submission.assignment.context, asset.submission.assignment]) %>
  <% else %>
    <%= polymorphic_url([asset.submission.assignment.context, asset.submission.assignment, :submission], id: asset.submission.user) %>
  <% end %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Submission Comment: %{user}, %{assignment}, %{context}",
        :user => submission_comment_submittor(asset, user),
        :assignment => asset.submission.assignment.title,
        :context => asset.submission.assignment.context.name %>
<% end %>

<%= t :body, "%{author} just made a new comment on the submission for %{user} for %{assignment}",
      :author => submission_comment_author(asset, user),
      :user => submission_comment_submittor(asset, user),
      :assignment => asset.submission.assignment.title %>

<%= asset.comment %>

<% if asset.media_comment? %>
  <%= t("This message includes media comments. To listen or reply, click this link: %{link}.  When allowed, if you need to include an attachment, please log in to <PERSON><PERSON> and reply to the submission.", link: content(:link)) %>
<% else %>
  <%= t("You can review the submission details by clicking this link: %{link}.", link: content(:link)) %>
<% end %>

<% unless asset.attachments.empty? %>
  <%= t "#messages.conversation_message.email.attached_files", "Attached Files:" %>
  <% asset.attachments.each do |attachment| %>
    <%= attachment.display_name %> - <%= attachment.readable_size %>
    <%= file_download_url(attachment) %>
  <% end %>
<% end %>
