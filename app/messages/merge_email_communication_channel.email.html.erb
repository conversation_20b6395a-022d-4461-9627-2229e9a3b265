<%
  current_host = (HostUrl.context_host(asset.merge_candidates(true).first.pseudonym.account) rescue nil) || HostUrl.default_host
  new_host     = (HostUrl.context_host((asset.pseudonym || asset.user.pseudonym).account) rescue nil) || HostUrl.default_host
%>

<% define_content :link do %>
  <%= registration_confirmation_url(asset.confirmation_code, host: current_host) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'New Canvas Account') %>
<% end %>
<p><%= t :account_created, "An account has been created for you at %{host}, but you already have a Canvas account.", :host => new_host %></p>
<p><%= t :merge2, "If you would like to merge them together, *click here*", :wrapper => "<a href=\"#{content(:link)}\">\\1</a>" %></p>
