<%=
    case asset.group.context_type
    when 'Account'
      t :body_account, "You've been added to a new group for the account %{account}.  The name of the group is %{group_name}.", :account => asset.group.context.name, :group_name => asset.group.name
    else
      t :body_course, "You've been added to a new group for the course %{course}.  The name of the group is %{group_name}.", :course => asset.group.context.name, :group_name => asset.group.name
    end
%>

<%= t :details, "More info at %{url}", :url => HostUrl.context_host(asset.group.context) %>