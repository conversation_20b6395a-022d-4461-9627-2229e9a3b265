<% define_content :link do %>
  <%= appointment_group_url(asset.appointment_group) %>
<% end %>

<% if asset.grants_right?(user, :read) -%>
<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t 'click_to_see_appointment', 'Click here to view the appointment' %>
  </a>
<% end %>
<% end -%>

<% courses = asset.appointment_group.participant_type == 'Group' ?
    asset.appointment_group.contexts.first.name  :
    asset.appointment_group.contexts_for_user(user).map(&:name).join(", ") %>
<% define_content :subject do %>
  <%= t('subject', 'Reservation canceled for %{appointment_name} (%{course})', :appointment_name => asset.title, :course => courses) %>
<% end %>

<p><%= t :message, '%{user} canceled his/her reservation for %{appointment_name}.', :user => data.updating_user_name || data.updating_user.name, :appointment_name => asset.title %></p>

<strong><%= t :details, 'Appointment Details' %>:</strong>
<p>
<table border="0" style="font-size: 14px; color: #444444;
    font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';
    border-collapse: collapse;">
    <tr>
        <td style="padding-right: 10px;" valign="top"><%= t :date_and_time, 'Date/Time' %>:</td>
        <td valign="top"><%= datetime_string(asset.start_at, asset.end_at) %></td>
    </tr>
    <% if asset.appointment_group.participant_type == 'Group' -%>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :attendees, 'Attendees' %>:</td>
          <td valign="top"><%= asset.participants.map(&:name).to_sentence %></td>
      </tr>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%=  t :group, 'Group' %>:</td>
          <td valign="top"><%= asset.context.name %></td>
      </tr>
    <% else -%>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :attendee, 'Attendee' %>:</td>
          <td valign="top"><%= asset.context.name %></td>
      </tr>
    <% end %>
    <tr>
      <td style="padding-right: 10px;" valign="top"><%= t 'Course' %>:</td>
      <td valign="top"><%= courses %></td>
    </tr>
    <% if asset.appointment_group.available_slots && asset.grants_right?(user, :read) -%>
      <tr>
          <td style="padding-right: 10px;" valign="top"><%= t :slots_remaining, 'Available time slots' %>:</td>
          <td valign="top"><%= asset.appointment_group.available_slots(current_only: true) %></td>
      </tr>
    <% end %>
</table>
<p style="font-weight: bold;"><%= t :cancel_reason, 'Reason for canceling' %></p>
<p><%= data.cancel_reason || t(:no_reason_given, "none given") %></p>
