<% define_content :link do %>
  <%= polymorphic_url([asset.context, :conferences]) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Web Conference Invitation: %{name}", :name => asset.context.name %>
<% end %>

<%= t :body, "You've been invited to participate in a web conference, %{title} for %{name}.", :title => asset.title, :name => asset.context.name %>

<%= t :details_link, "You can see the details here: %{link}", :link => content(:link) %>
