<% define_content :link do %>
  <%= polymorphic_url([asset.context, :file], id: asset) %>
<% end %>
<% define_content :subject do -%>
<%= t('subject', 'New File Added: %{title}, %{name}', :title => asset.title, :name => asset.context.name) %>
<% end -%>
<%= t('body', 'A new file has been added for the course, %{name}:', :name => asset.context.name) %>

<%= asset.title %> (<%= asset.readable_size %>)

<%= t 'link', 'You can view it here' %>
<%= content :link %>
