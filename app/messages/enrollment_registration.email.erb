<% define_content :link do %>
  <%= course_url(asset.course, invitation: asset.uuid) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Course Invitation" %>
<% end %>

<%= t "You've been invited to participate in a class at %{account}.  The class is called %{course}. Course role: %{role_type}",
    :account => asset.course.root_account.name, :course => asset.course.name, :role_type => asset.readable_role_name %>

<% email = asset.user.email; login = (asset.user.pseudonym.unique_id rescue "none") %>
<%= before_label :name, "Name" %> <%= asset.user.name %>
<%= before_label :email, "Email" %> <%= asset.user.email %>
<% if email != login %><%= before_label :username, "Username" %> <%= asset.user.pseudonym.unique_id rescue t(:none, "none") %><% end %>

<%= t :details, "You'll need to register with <PERSON><PERSON> before you can participate in the class.  To get started, visit the course page here:" %>
<%= content :link %>
