<% # evizitei: this template is used for submissions AND quiz_submissions, make sure methods used are available on both models %>

<% define_content :link do %>
  <% if asset.assignment.context.feature_enabled?(:assignments_2_student) %>
    <%= course_assignment_url(asset.assignment.context, asset.assignment) %>
  <% else %>
    <%= course_assignment_submission_url(asset.assignment.context, asset.assignment, asset.user_id) %>
  <% end %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Assignment Graded: %{assignment}, %{context}", :assignment => asset.assignment.title, :context => asset.assignment.context.name %>
<% end %>
<%
  tag = ''
  if asset.assignment.is_a?(SubAssignment)
    tag = case asset.assignment.sub_assignment_tag
            when CheckpointLabels::REPLY_TO_TOPIC
              t('Reply To Topic')
            when CheckpointLabels::REPLY_TO_ENTRY
              t('Reply To Entry')
            else
              ''
          end
  end
  assignment_title = "#{asset.assignment.title} #{tag}".strip
%>
<% if send_student_names(asset, user) %>
<%= t "%{assignment} has been graded for %{name}.", assignment: assignment_title, name: asset.user.name %>
<% else %>
<%= t :body, "Your assignment %{assignment} has been graded.", :assignment => assignment_title %>
<% end %>

<%= t :graded_date, "graded: %{date}", :date => (datetime_string(force_zone(asset.graded_at)) rescue t(:no_date_set, "No Date Set")) %>
<% if user.try(:send_scores_in_emails?, asset.assignment.context) %>
  <% if asset.excused? %>
<%= t :excused, "This assignment has been excused." %>
  <% elsif asset.score %>
    <% if asset.assignment.restrict_quantitative_data?(user)%>
<%=t :grade, "grade: %{letter_grade}", :letter_grade => asset.assignment.score_to_grade(asset.score, asset.grade, true)%>
    <% else %>
<%= t :score, "score: %{score} out of %{total}", :score => asset.score, :total => (asset.assignment.points_possible || t(:not_applicable, "N/A")) %>
    <% end %>
  <% end %>
<% end %>
<%= t(:score_pending_review, "score pending review by the teacher") if asset.pending_review? %>

<%= t(:link_message, "You can review the assignment here:") %>
<%= content :link %>
