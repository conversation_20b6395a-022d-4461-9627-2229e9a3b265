<% define_content :link do %>
  <%= conversation_url(asset.conversation_id) %>
<% end %>
<% define_content :user_name do %>
  <%= asset.author_short_name_with_shared_contexts(user) rescue t(:unknown_user, "Unknown User") %>
<% end %>
<% define_content :subject do %>
  <%= t :subject, "%{user_name}, you just sent a message in Canvas.", :user_name => content(:user_name) %>
<% end %>

<%= t :body, "%{user_name}, you just sent a message in Canvas:", :user_name => content(:user_name) %>

<%= CanvasTextHelper.truncate_text(asset.conversation.subject.present? ? <<HEREDOC : asset.body, :max_length => 200)
Subject: #{asset.conversation.subject}

#{asset.body}
HEREDOC
%>
