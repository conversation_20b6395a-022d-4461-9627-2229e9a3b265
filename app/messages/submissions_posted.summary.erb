<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% body_text = "" %>

<%
  if @data.graded_only
    if @data.section_names.present?
      body_text = t("Grade changes and comments have been released for everyone graded in sections: %{section_names}.", section_names: @data.section_names.to_sentence)
    else
      body_text = t("Grade changes and comments have been released for everyone graded.")
    end
  else
    if @data.section_names.present?
      body_text = t("Grade changes and comments have been released for everyone in sections: %{section_names}.", section_names: @data.section_names.to_sentence)
    else
      body_text = t("Grade changes and comments have been released for everyone.")
    end
  end
%>

<% define_content :subject do %>
  <%= body_text %>
<% end %>
