<%= t :body_sms, "Your grade for %{title}, %{context} just changed.", :title => asset.assignment.title, :context => asset.assignment.context.name %>

<%= t :regraded_date, "re-graded: %{date}", :date => (datetime_string(force_zone(asset.graded_at)) rescue t(:no_date_set, "No Date Set")) %>
<% if asset.score && user.try(:send_scores_in_emails?, asset.assignment.context) %>
  <%= t :score, "score:  %{score} out of %{total}", :score => asset.score, :total => (asset.assignment.points_possible || t(:not_applicable, "N/A")) %>
<% end %>
<%= t(:score_pending, "score pending review by the teacher") if asset.pending_review? %>

<%= t :more_info, "More info at %{url}", :url => HostUrl.context_host(asset.assignment.context) %>
