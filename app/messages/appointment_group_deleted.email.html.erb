<% define_content :link do %>
  <%= appointment_groups_url %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'Appointments for %{appointment_name} have been canceled (%{course})', :appointment_name => asset.title, :course => asset.contexts_for_user(user).map(&:name).join(", ")) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_text, 'View the appointment' %>
  </a>
<% end %>

<p><%= t :message, 'All appointments for %{appointment_name} have been canceled.', :appointment_name => asset.title %></p>

<p><strong><%= t :details, 'Appointment Details' %>:</strong></p>
<table border="0" style="font-size: 14px; color: #444444;
    font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';
    border-collapse: collapse;">
    <% if asset.end_at %>
    <tr>
        <td style="padding-right: 10px;" valign="top"><%= t :dates, 'Date(s)' %>:</td>
        <td valign="top"><%= date_string(asset.start_at, asset.end_at, :no_words) %></td>
    </tr>
    <% end %>
    <tr>
        <td style="padding-right: 10px;" valign="top"><%= t :course, 'Course' %>:</td>
        <td valign="top"><%=
            asset.participant_type == 'Group' ?
                asset.contexts.first.name :
                asset.contexts_for_user(user).map(&:name).join(", ") %></td>
    </tr>
</table>
<p style="font-weight: bold;"><%= t :cancel_reason, 'Reason for canceling' %></p>
<p><%= data.cancel_reason || t(:no_reason_given, "none given") %></p>
