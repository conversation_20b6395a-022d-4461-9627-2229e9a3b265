<% define_content :link do -%>
  <%= dashboard_url %>
<% end -%>
<% define_content :subject do -%>
  <%= t :subject, "Recent Canvas Notifications" %>
<% end -%>
<% m = (delayed_messages || []).find{|m| m.frequency == Notification::FREQ_WEEKLY} || delayed_messages.first %>
<% if !m || m.frequency == Notification::FREQ_DAILY %>
  <%= t :daily_body, "You're signed up to receive a daily report of some notifications from your Canvas account.  Below is the report for %{date}:", :date => (date_string(force_zone(m.send_at), :no_words) rescue t("#date.days.today_lower", "today")) %>
<% elsif m.frequency == Notification::FREQ_WEEKLY %>
  <%= t :weekly_body, "You're signed up to receive a weekly report of some notifications from your Canvas account.  Below is the report for the week ending %{date}:", :date => (date_string(force_zone(m.send_at), :no_words) rescue t("#date.days.today_lower", "today")) %>
<% end %>

<% for delayed_message in delayed_messages do %>
--------------------------------

<%= delayed_message.name_of_topic %>

    <%= CanvasTextHelper.indent(html_to_text(delayed_message.summary, :base_url => content(:link)), 4) %>

    <%= delayed_message.link %>
<% end %>


<%= t :notifications_link, "You can change your notification settings by visiting the following page:" %>
<%= communication_profile_url %>
