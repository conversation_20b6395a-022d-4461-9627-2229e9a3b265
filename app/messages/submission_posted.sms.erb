<% assignment = asset.assignment %>

<% define_content :link do %>
  <%= polymorphic_url([assignment.context, assignment, asset]) %>
<% end %>

<%= t :body, "Your instructor has released grade changes and new comments for %{title}, %{course}.",
      title: assignment.title, course: assignment.context.name %>

<% if asset.graded_at %>
  <%= t :graded_date, "graded: %{date}", :date => (datetime_string(force_zone(asset.graded_at) || t("No Date Set")) rescue t(:no_date_set, "No Date Set")) %>
<% end %>

<% if asset.score && user.try(:send_scores_in_emails?, assignment.context) %>
  <% if asset.assignment.restrict_quantitative_data?(user)%>
    <%=t :grade, "grade: %{letter_grade}", :letter_grade => asset.assignment.score_to_grade(asset.score, asset.grade, true)%>
  <% else %>
    <%= t :score, "score: %{score} out of %{total}", :score => asset.score, :total => (asset.assignment.points_possible || t(:not_applicable, "N/A")) %>
  <% end %>
<% end %>

<%= t :link_message, "More info at %{link}.", link: content(:link) %>
