<% presenter = Messages::SubmissionCommentForTeacher::EmailPresenter.new(self) %>

<% define_content :link do %>
  <%= presenter.link %>
<% end %>

<% define_content :subject do %>
  <%= presenter.subject %>
<% end %>

<%= presenter.body %>

<%= presenter.comment_text %>

<% if presenter.media_comment? %>
  <%= t("This message includes media comments. To listen or reply, click this link: %{link}.", link: presenter.link) %>
<% elsif presenter.can_reply? %>
  <%= t("You can reply to this comment by responding to this message, or review the submission details using this link: %{link}.  When allowed, if you need to include an attachment, please log in to <PERSON><PERSON> and reply to the submission", link: presenter.link) %>
<% else %>
  <%= t("You can review the submission details using this link: %{link}.", link: presenter.link) %>
<% end %>

<% unless presenter.attachments.empty? %>
  <%= t("Attached Files:") %>
  <% presenter.attachments.each do |attachment| %>
    <%= attachment.display_name %> - <%= attachment.readable_size %>
    <%= file_download_url(attachment) %>
  <% end %>
<% end %>
