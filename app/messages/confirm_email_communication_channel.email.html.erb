<% define_content :link do %>
  <%= registration_confirmation_url(asset.confirmation_code) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Confirm Email: Canvas" %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :details, "Click here to confirm this registration" %>
  </a>
<% end %>

<p>
<%= t :body,
      "The email address: %{email} is being added to a Canvas account registered at %{website}.",
      :email => asset.path,
      :website => data.from_host || HostUrl.default_host %>
</p>

<%= t :reject_message, "To prevent this address from being registered, please disregard this email." %>
