<% parent_assignment = asset.parent_assignment %>
<% define_content :link do %>
  <%= polymorphic_url([parent_assignment.context, parent_assignment]) %>
<% end %>

<% define_content :subject do %>
  <%= I18n.t("Assignment Created - %{assignment_name}, %{course_name}", :assignment_name => parent_assignment.title, :course_name => parent_assignment.context.name) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= I18n.t("View the assignment") %>
  </a>
<% end %>

<p><%= I18n.t("A new assignment has been created for your course, %{course_name}",
              :course_name => parent_assignment.context.name) %></p>

<hr style="border: 0;
  height: 1px;
  background: #333;
  background-image: -webkit-linear-gradient(left, #ddd, #aaa, #eee);
  background-image:    -moz-linear-gradient(left, #ddd, #aaa, #eee);
  background-image:     -ms-linear-gradient(left, #ddd, #aaa, #eee);
  background-image:      -o-linear-gradient(left, #ddd, #aaa, #eee);" />

<p style="font-weight: bold;"><%= parent_assignment.title %></p>

<%
  reply_to_topic_checkpoint = parent_assignment.sub_assignments.find_by(sub_assignment_tag: CheckpointLabels::REPLY_TO_TOPIC)
  reply_to_entry_checkpoint = parent_assignment.sub_assignments.find_by(sub_assignment_tag: CheckpointLabels::REPLY_TO_ENTRY)

  reply_to_topic_due_date = reply_to_topic_checkpoint.due_at ? datetime_string(force_zone(reply_to_topic_checkpoint.due_at)) : I18n.t("No Due Date")
  reply_to_entry_due_date = reply_to_entry_checkpoint.due_at ? datetime_string(force_zone(reply_to_entry_checkpoint.due_at)) : I18n.t("No Due Date")

  discussion_topic = parent_assignment.discussion_topic
  reply_to_entry_required_count = discussion_topic.reply_to_entry_required_count
%>

<p>
  <%= I18n.t("due: reply to topic: %{assignment_due_date_time}", :assignment_due_date_time => reply_to_topic_due_date) %><br />
  &nbsp;&nbsp;&nbsp;&nbsp;<%= I18n.t("additional replies (%{reply_to_entry_required_count}): %{assignment_due_date_time}", :reply_to_entry_required_count => reply_to_entry_required_count, :assignment_due_date_time => reply_to_entry_due_date) %>
</p>
