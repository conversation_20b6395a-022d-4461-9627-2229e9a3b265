<% define_content :link do %>
<%# TODO: once react_discussions_post is no longer feature_flag_dependent, remove the anchor param %>
  <%= polymorphic_url([asset.context, :discussion_topic], id: asset.discussion_entry.discussion_topic_id, anchor: "entry-#{asset.discussion_entry_id}", entry_id: asset.discussion_entry_id) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "You have been mentioned in %{discussion_topic}: %{course}", discussion_topic: asset.discussion_entry.title, course: asset.context.name %>
<% end %>

<%= CanvasTextHelper.truncate_text(html_to_text(asset.message, base_url: dashboard_url), max_length: 200) %>
