<%= t "New event for %{course_name}", :course_name => asset.context.name %>

<%= asset.title %>

<% if !asset.start_at && !asset.end_at %>
  <%= t('no_time_set', 'No Time Set') %>
<% elsif (asset.start_at == asset.end_at || !asset.end_at) %>
  <%= datetime_string(force_zone(asset.start_at)) %>
<% else %>
  <%= t('from_to_time', 'from %{start_date_time} to %{end_date_time}', :start_date_time => datetime_string(force_zone(asset.start_at)), :end_date_time => datetime_string(force_zone(asset.end_at))) %>
<% end %>
<% if asset.series_uuid %>
  <%= rrule_to_natural_language(asset.rrule) %>
<% end %>

<%= t('more_info', 'More info at %{web_address}', :web_address => HostUrl.context_host(asset.context)) %>
