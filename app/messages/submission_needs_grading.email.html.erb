<% define_content :link do %>
  <%= course_quiz_history_url(asset.quiz.context, asset.quiz, quiz_submission_id: asset.id) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Quiz Submitted: %{quiz}, %{context}", :quiz => asset.quiz.title, :context => asset.assignment.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_message, "You can review the submission here" %>
  </a>
<% end %>

<p><%= t :body, "%{user_name} has completed the quiz %{quiz_title}, and it requires manual grading.", :user_name => asset.user.name, :quiz_title => asset.quiz.title %></p>

<p><%= t :submitted_date, "submitted: %{date}", :date => datetime_string(force_zone(asset.finished_at))  %></p>
