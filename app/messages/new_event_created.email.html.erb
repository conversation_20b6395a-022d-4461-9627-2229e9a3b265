<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'New Event - %{event_title}, %{course_name}', :event_title => asset.title, :course_name => asset.context.name) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t 'see_details', 'You can see details here' %>
  </a>
<% end %>

<p><%= t('body', "There's a new event scheduled for %{course_name} that you should be aware of:", :course_name => asset.context.name) %></p>

<p><strong><%= asset.title %></strong></p>

<p><em style="color: #00ae00;">
  <% if !asset.start_at && !asset.end_at %>
    <%= t('no_time_set', 'No Time Set') %>
  <% elsif (asset.start_at == asset.end_at || !asset.end_at) %>
    <%= datetime_string(force_zone(asset.start_at)) %>
  <% else %>
    <%= t('from_to_time', 'from %{start_date_time} to %{end_date_time}', :start_date_time => datetime_string(force_zone(asset.start_at)), :end_date_time => datetime_string(force_zone(asset.end_at))) %>
  <% end %>
</em>
  <% if asset.series_uuid %>
    <div>
      <%= rrule_to_natural_language(asset.rrule) %>
    </div>
  <% end %>
</p>

