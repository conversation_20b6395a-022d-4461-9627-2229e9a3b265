<% p = asset.is_a?(Pseudonym) ? asset : asset.pseudonym %>
<% cc = asset.is_a?(CommunicationChannel) ? asset : (p.communication_channel || p.user.communication_channel) %>
<% define_content :link do %>
  <%= p.user.registered? ? profile_url : registration_confirmation_url(cc.confirmation_code) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Finish Registration: Canvas" %>
<% end %>

<% remove_profile_url_in_layout = true %>

<%= t :body, "You have been registered for a Canvas account at %{account}!  Before you can log in and start using Canvas, you'll need to finish the configuration process.", :account => p.account.display_name %>

<%= t :link_message, "To finish the registration process, please visit the following url:" %>
<%= content :link %>
