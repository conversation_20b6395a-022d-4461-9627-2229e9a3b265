<% define_content :link do %>
  <%= course_url(asset) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "New Course" %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "Click here to view the course" %>
  </a>
<% end %>

<p><%= t :title, "New Course for %{account}", :account => asset.root_account.name %></p>

<p><%= t :body, "A course, %{course}, has been added for the account %{account}", :course => asset.name, :account => asset.root_account.name %></p>

<% if !asset.teachers.empty? %>
  <p>
    <strong><%= before_label :teachers, "Teachers" %></strong>
    <ul style="padding: 0; list-style: none;">
      <% asset.teachers.each do |teacher| %>
        <li>
          <a href="<%= course_user_url(asset, teacher) %>"><%= teacher.name %></a>
          <% if teacher.email %>(<%= teacher.email %>)<% end %>
        </li>
      <% end %>
    </ul>
  </p>
<% end %>
