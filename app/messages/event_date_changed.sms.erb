<%= t('%{event_title}, %{course_name} changed to', :event_title => asset.title, :course_name => asset.context.name) %>

<% if !asset.start_at && !asset.end_at %>
  <%= t('no_time_set', 'No Time Set') %>
<% elsif (asset.start_at == asset.end_at || !asset.end_at) %>
  <%= datetime_string(force_zone(asset.start_at)) %>
<% else %>
  <%= t('from_to_time', 'from %{start_date_time} to %{end_date_time}', :start_date_time => datetime_string(force_zone(asset.start_at)), :end_date_time => datetime_string(force_zone(asset.end_at))) %>
<% end %>

<%= t('more_info', 'More info at %{web_link}', :web_link => HostUrl.context_host(asset.context)) %>
