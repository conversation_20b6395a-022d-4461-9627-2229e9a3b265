<%= t :body, "A new course, %{course}, has been added for the account %{account}", :course => asset.name, :account => asset.root_account.name %>
<% if !asset.teachers.empty? %>
<%= before_label :teachers, "Teachers" %>
<% asset.teachers.each do |teacher| %>
  - <%= teacher.name %> (<%= teacher.email %>)
    
<% end %><% end %>

<%= t :details, "More info at %{url}", :url => HostUrl.context_host(asset) %>