<%=
    case asset.group.context_type
    when 'Account'
      t :body_account, "You've been invited to join the group %{group} for the account %{account}.", :group => asset.group.name, :account => asset.group.context.name
    else
      t :body_course, "You've been invited to join the group %{group} for the course %{course}.", :group => asset.group.name, :course => asset.group.context.name
    end
%>

<%= t :details, "More info at %{url}", :url => HostUrl.context_host(asset.group.context) %>