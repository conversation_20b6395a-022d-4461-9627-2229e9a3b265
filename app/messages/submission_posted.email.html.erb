<% assignment = asset.assignment %>

<% define_content :link do %>
  <%= polymorphic_url([assignment.context, assignment, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= t "Submission Posted: %{title}, %{course}", title: assignment.title, course: assignment.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "You can view it here" %>
  </a>
<% end %>

<p><%= t :body, "Your instructor has released grade changes and new comments for %{title}. These changes are now viewable.", :title => assignment.title %></p>

<% if asset.graded_at %>
  <p><%= t :graded_date, "graded: %{date}", :date => (datetime_string(force_zone(asset.graded_at) || t("No Date Set")) rescue t(:no_date_set, "No Date Set")) %></p>
<% end %>

<% if user.try(:send_scores_in_emails?, assignment.context) %>
  <% if asset.excused? %>
    <p><%= t :excused, "This assignment has been excused." %></p>
  <% elsif asset.score %>
    <% if assignment.restrict_quantitative_data?(user)%>
      <p><%=t :grade, "grade: %{letter_grade}", :letter_grade => assignment.score_to_grade(asset.score, asset.grade, true)%></p>
    <% else %>
      <p><%= t :score, "score: %{score} out of %{total}", :score => asset.score, :total => (assignment.points_possible || t(:not_applicable, "N/A")) %></p>
    <% end %>
  <% end %>
<% end %>

<p><%= t(:score_pending_review, "score pending review by the teacher") if asset.pending_review? %></p>
