<% assignment = asset.assignment %>

<% define_content :link do %>
  <%= polymorphic_url([assignment.context, assignment, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= t "Submission Posted: %{title}, %{course}", title: assignment.title, course: assignment.context.name %>
<% end %>

<%= t :body, "Your instructor has released grade changes and new comments for %{title}. These changes are now viewable.", title: assignment.title %>
<% if asset.graded_at %>
  <%= t :graded_date, "graded: %{date}", :date => (datetime_string(force_zone(asset.graded_at) || t("No Date Set")) rescue t(:no_date_set, "No Date Set")) %>
<% end %>

<% if user.try(:send_scores_in_emails?, asset.assignment.context) %>
  <% if asset.excused? %>
    <%= t :excused, "This assignment has been excused." %>
  <% elsif asset.score %>
    <% if assignment.restrict_quantitative_data?(user)%>
      <%=t :grade, "grade: %{letter_grade}", :letter_grade => assignment.score_to_grade(asset.score, asset.grade, true)%>
    <% else %>
      <%= t :score, "score: %{score} out of %{total}", :score => asset.score, :total => (assignment.points_possible || t(:not_applicable, "N/A")) %>
    <% end %>
  <% end %>
<% end %>
<%= t(:score_pending_review, "score pending review by the teacher") if asset.pending_review? %>

<%= t :link_message, "You can view it here: %{link}.", link: content(:link) %>
