<% define_content :link do %>
  <%= login_url %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'Forgot Password: LMS') %>
<% end %>

<% pseudonyms = asset.user.pseudonyms_visible_to(asset.user) %>
<% first_pseudonym = pseudonyms.first %>
<%= t('requested_password_reset', 'You requested a confirmation of your password for logging into LMS.') %>
<% if pseudonyms.length > 1 %><%= before_label('associated_with_accounts', 'This address is associated with the following accounts') %>

<% pseudonyms.each do |p| %>
- <%= t('login_at_account', '%{login_email} at %{account_name}', :login_email => p.unique_id, :account_name => p.account.display_name) %>
    <% if p.managed_password? %><%= t('login_managed_by_account', "this login's credentials are managed by %{account_name}", :account_name => p.account.name) %><% else %><%= before_label('change_password_at', "change this login's password at") %>
      <% channel = p.communication_channel || asset %>
      <%= confirm_change_password_url(p, channel.confirmation_code, host: HostUrl.context_host(p.account)) %>
    <% end %>

<% end %>
<% else %>
  <%= t('associated_login', 'This address is associated with the login, "%{login_identifier}".', :login_identifier => first_pseudonym.unique_id) %><% if first_pseudonym.managed_password? %>  <%= t('password_from_account', "The password for this login should have been given to you by the system administrators at %{account_name}, and Instructure doesn't have access to your password.  If your password is not working, please contact the system administrators about changing or verifying your password.", :account_name => first_pseudonym.account.display_name) %><% else %>

  <%= before_label('click_to_reset', 'To set a new password, please click the following link') %>
  <%= confirm_change_password_url(first_pseudonym, asset.confirmation_code, host: HostUrl.context_host(first_pseudonym.account)) %>
<% end %>
<% end %>
