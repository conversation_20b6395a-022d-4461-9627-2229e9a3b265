<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "Click here to view the assignment" %>
  </a>
<% end %>
  
<% define_content :subject do %>
  <%= t :subject, "Assignment Changed: %{title}, %{course}", :title => asset.title, :course => asset.context.name %>
<% end %>

<p><%= t :body, 'The assignment "%{title}" for %{course} has changed.', :title => asset.title, :course => asset.context.name %></p>
