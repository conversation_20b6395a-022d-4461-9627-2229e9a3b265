<% define_content :link do %>
  <%= polymorphic_url([asset.context, :files]) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', '%{count} new files added for the course, %{name}',
          :count => n(data.count),
          :name => asset.context.name) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t 'see_details', 'You can view them here' %>
  </a>
<% end %>

<p><%= t('body', '%{count} new files have been added for the course, %{name}:',
        :count => n(data.count),
        :name => asset.context.name) %></p>

<ul>
  <% data.display_names.first(Attachment::NOTIFICATION_MAX_DISPLAY).each do |title| %>
    <li><%= title %></li>
  <% end %>
</ul>
<% if data.count > Attachment::NOTIFICATION_MAX_DISPLAY %>
  <p><%= t('The first %{max_displayed} files are listed above. Click below to view all files.',
  :max_displayed => Attachment::NOTIFICATION_MAX_DISPLAY)%></p>
<% end %>

