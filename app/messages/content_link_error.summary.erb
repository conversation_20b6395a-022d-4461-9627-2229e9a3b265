<% define_content :subject do %>
  <%= t('subject', 'Content Link Error - %{assignment_name}', assignment_name: asset.title) %>
<% end %>

<% define_content :link do %>
  <%= course_link_validator_url(asset.context) %>
<% end %>

<% bad_link = "<a href='#{h(data.url)}'>#{h(data.anchor)}</a>" %>
<% asset_link = "<a href='#{h(data.location)}'>#{h(asset.title)}</a>" %>
<% if data.error_type == :unpublished_item %>
  <% display_text = I18n.t("Students are attempting to access unpublished content, %{bad_link}, in %{asset_link}.", bad_link: bad_link, asset_link: asset_link) %>
<% elsif data.error_type == :deleted %>
  <% display_text = I18n.t("Students are clicking on %{bad_link} to content that has been removed in %{asset_link}.", bad_link: bad_link, asset_link: asset_link) %>
<% elsif data.error_type == :course_mismatch %>
  <% display_text = I18n.t("In %{asset_link}, students are clicking %{bad_link} to content in a different course.", bad_link: bad_link, asset_link: asset_link) %>
<% elsif data.error_type == :inaccessible %>
  <% display_text = I18n.t("In %{asset_link}, students are clicking %{bad_link} to content they cannot access.", bad_link: bad_link, asset_link: asset_link) %>
<% else %>
  <% display_text = I18n.t("In %{asset_link}, students are clicking %{bad_link} to a non-existent page in your course.", bad_link: bad_link, asset_link: asset_link) %>
<% end %>

<%= display_text %>

<%= I18n.t("To check for other broken links, visit the Course Link Validator") %>