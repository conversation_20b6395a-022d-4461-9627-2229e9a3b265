<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset.asset.assignment, :submission], id: asset.asset.user_id) %>
<% end %>

<% define_content :subject do %>
  <%= t('#messages.peer_review_invitation.email.subject', "Assigned peer review for %{reviewee}", :reviewee => reviewee_name(asset, user))%>
<% end %>

<%=
  t("You've been invited to peer review %{reviewee}. Follow this link to review them: %{link}.", {
    reviewee: reviewee_name(asset, user),
    link: content(:link)
  })
%>
