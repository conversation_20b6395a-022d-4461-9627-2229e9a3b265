<% course = asset.is_a?(Course) ? asset : asset.context %>
<% define_content :link do %>
  <%= course_assignments_url(course) %>
<% end %>

<% define_content :subject do %>
  <%= before_label('subject', 'Grade Weight Changed') %> <%= asset.name %>
<% end %>

<%= t('grade_changed', "The grading policy for the course, %{course_name} has changed.", :course_name => course.name) %>
<% if course.group_weighting_scheme == 'percent' %>
<%= before_label('grades_based_on_percent', 'Grading is based on the weighted total for each assignment group') %>
<% course.assignment_groups.active.each do |group |%>
- <%= group.name %>: <%= group.group_weight %>%
<% end %>
<% else %>
<%= t('grading_based_on_points', 'Grading is based on total points for all assignments.') %>
<% end %>

<%= before_label('now_see_here', 'You can see details here') %>
<%= content :link %>
