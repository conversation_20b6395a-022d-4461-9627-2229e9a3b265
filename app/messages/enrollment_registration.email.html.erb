<% define_content :subject do %>
  <%= t :subject, "Course Invitation" %>
<% end %>

<% remove_profile_url_in_layout = true %>

<% email = asset.user.email; login = (asset.user.pseudonym.unique_id rescue "none") %>

<p>
  <%= t :body, "You've been invited to participate in a class at %{account}.  The class is called %{course}. Course role: %{role_type}",
      :account => asset.course.root_account.name, :course => asset.course.name, :role_type => asset.readable_role_name %>
</p>

<table border="0" style="font-size: 14px; color: #444444;
    font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';
    border-collapse: collapse;">
    <tr>
        <td style="padding-right: 10px;"><%= t(:name, 'Name') %>:</td>
        <td style="font-weight: bold;"><%= asset.user.name %></td>
    </tr>
    <tr>
        <td style="padding-right: 10px"><%= t(:email, 'Email') %>:</td>
        <td style="font-weight: bold;"><%= email %></td>
    </tr>
    <% if email != login %>
      <tr>
          <td style="padding-right: 10px;"><%= t(:username, 'Username') %>:</td>
          <td style="font-weight: bold;"><%= asset.user.pseudonym.unique_id rescue t(:none, "none") %></td>
      </tr>
    <% end %>
</table>

<p><%= t :details, "You'll need to register with Canvas before you can participate in the class." %></p>

<br><br>

<table width="100%" border="0" style="text-align:center;">
  <tr>
    <td>
      <a href="<%= content(:link) %>" style="color: #FFFFFF; background-color: #0374B5; padding: 8px 40px; border-radius: 3px; text-decoration: none; font-weight: bold;">
        <%= t('Get Started') %>
      </a>
    </td>
  </tr>
</table>
