<% define_content :link do %>
<%# TODO: once react_discussions_post is no longer feature_flag_dependent, remove the anchor param %>
  <%= polymorphic_url([asset.context, :discussion_topic], id: asset.discussion_topic_id, anchor: "entry-#{asset.id}", entry_id: asset.id) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "New Comment on Announcement %{discussion_topic}: %{course}", :discussion_topic => asset.title, :course => asset.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "Click here to view the announcement" %>
  </a>
<% end %>

<p><%= t :body, "%{user} posted a new comment on the announcement: %{discussion_topic} for %{course}:", :user => asset.user.short_name, :discussion_topic => asset.title, :course => asset.context.name %></p>

<p>
  <%= html_to_simple_html(asset.message, :base_url => dashboard_url) %>
</p>

<% if avatar_enabled? %>
  <table border="0" style="border-collapse: collapse">
    <tr height="30px">
      <td></td>
    </tr>
    <tr >
      <td align="left" width="50" style="width: 50px"><img style="border-radius: 50px; height: 50px; width: 50px;" height="50" width="50" src="<%=author_avatar_url%>" alt="<%=author_short_name%>"> </td>
      <td width="10"></td>
      <td>
        <table border="0" style="font-size: 14px; color: #444444; background-color: #ffffff; font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';" valign="top" align="left">
          <tr>
            <td valign="bottom" align="left">
              <b><%= author_short_name%></b>
            </td>
          </tr>
          <tr>
            <td valign="top" align="left">
              <a href="mailto:<%= author_email_address %>">
                <%= author_email_address%>
              </a>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
<% else %>
  <p height="30px"></p>
  <p>
    <b><%= author_short_name%></b>
    <br/>
    <a href="mailto:<%= author_email_address %>">
      <%= author_email_address%>
    </a>
  </p>
<% end %>

<% if IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
<p><%= t :discussion_entry_reply_message, "Join the conversation using the link below, or comment by replying to this message.  When allowed, if you need to include an attachment, please log in to Canvas and reply to the announcement." %></p>
<% else %>
<p><%= t "Join the conversation using the link below." %></p>
<% end %>
