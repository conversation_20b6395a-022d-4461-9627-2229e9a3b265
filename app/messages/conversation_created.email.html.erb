<% define_content :link do %>
  <%= conversation_url(asset.conversation_id) %>
<% end %>

<% define_content :user_name do %>
  <%= asset.author_short_name_with_shared_contexts(user) rescue t(:unknown_user, "Unknown User") %>
<% end %>

<% define_content :subject do %>
  <% if IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
    <%= t "%{user_name}, you just sent a message in Canvas.", :user_name => content(:user_name) %>
  <% else %>
    <%= t "%{user_name}, you just sent a message in Canvas - Do not reply", :user_name => content(:user_name) %>
  <% end %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content :link %>">
    <% if IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
      <%= t "View this message in Conversations" %>
    <% else %>
      <%= t "Reply to this message in Canvas" %>
    <% end %>
  </a>
<% end %>

<% if asset.conversation.subject.present? %>
<h4>
  <%= format_message(asset.conversation.subject).first %>
</h4>
<% end %>

<p>
  <%= format_message(asset.body).first %>
</p>

<% if avatar_enabled? %>
  <table border="0" style="border-collapse: collapse">
    <tr height="30px">
      <td></td>
    </tr>
    <tr >
      <td align="left" width="50px" ><img style="border-radius: 50px;" height= 50px width = 50px src= <%=author_avatar_url%> alt=<%=author_short_name%>> </td>
      <td width="10"></td>
      <td>
        <table border="0" style="font-size: 14px; color: #444444; background-color: #ffffff; font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';" valign="top" align="left">
          <tr>
            <td valign="bottom" align="left">
              <% if IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
                <b><%= author_short_name%></b>
              <% else %>
                <b><%= author_short_name%> <%= t"Please do not reply to this email, it will not be delivered." %></b>
              <% end %>
            </td>
          </tr>
          <tr>
            <td valign="top" align="left">
              <a href="<%= content :link %>">
                <%= author_email_address%>
              </a>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
<% else %>
  <p height="30px"></p>
  <p>
    <% if IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
      <b><%= author_short_name%></b>
    <% else %>
      <b><%= author_short_name%> <%= t"Please do not reply to this email, it will not be delivered." %></b>
    <% end %>
    <br/>
    <a href="<%= content :link %>">
      <%= author_email_address%>
    </a>
  </p>
<% end %>

<% if asset.has_media_objects? %>
<p><%= t :audio_comment, "This message includes media comments. To listen or reply, click the link below:" %></p>
<% elsif IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
<p><%= t :reply_to_conversation_message, "You can reply to this message in Canvas by replying directly to this email.  If you need to include an attachment, please log in to Canvas and reply through the Inbox." %></p>
<% else %>
  <p><%=t :pre_you_can, "You can "%><a href="<%= content :link %>"><%= t"reply to this message in Canvas." %></a></p>
<% end %>

<% unless asset.attachments.empty? %>
<div style="margin-top: 20px; padding: 10px; background-color: #f3f3f3;">
  <p style="margin: 0px;"><%= t :attached_files, "Attached Files:" %></p>
  <ul style="padding-left: 20px;">
    <% asset.attachments.each do |attachment| %>
    <li><%= attachment.display_name %> - <%= attachment.readable_size %> <a href="<%= file_download_url(attachment, verifier: attachment.uuid) %>">[<%= t :download, "download" %>]</a></li>
    <% end %>
  </ul>
</div>
<% end %>
