<% define_content :link do %>
  <%= polymorphic_url([asset.context, :wiki_page], id: asset.url) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Updated Page: %{title}, %{user}", :title => asset.title.titleize, :user => asset.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_message, "Click here to review it" %>
  </a>
<% end %>

<p><%= t :body, "The page %{title} has been updated.", :title => asset.context.name %></p>

<p><strong><%= html_to_text(asset.title).titleize %></strong></p>

<% available_for = asset.available_for?(user) %>
<% if available_for %>
  <p><%= html_to_text(asset.body, :base_url => dashboard_url) %></p>
<% else %>
  <p><%= t("Wiki page content is locked or not yet available") %></p>
<% end %>