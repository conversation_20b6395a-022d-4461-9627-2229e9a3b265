<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= t('assignment_graded', 'Assignment Graded: %{assignment_name}, %{course_name}', :assignment_name => asset.title, :course_name => asset.context.name) %>
<% end %>

<%= t('the_assignment_is_graded', 'The assignment, %{assignment_name}, has been graded.', :assignment_name => asset.title) %>
