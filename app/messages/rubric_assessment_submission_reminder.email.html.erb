<% define_content :link do %>
  <%=
    asset.submission.assignment.anonymous_peer_reviews? ?
      polymorphic_url([asset.submission.assignment.context, asset.submission.assignment, :anonymous_submission], anonymous_id: asset.submission.anonymous_id) :
      polymorphic_url([asset.submission.assignment.context, asset.submission.assignment, :submission], id: asset.submission.user_id)
  %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Reminder to Assess: %{rubric}, %{context}", :rubric => asset.submission.assignment.title, :context => asset.rubric_association.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_message, "Click here to review the assessment and submit your entry" %>
  </a>
<% end %>

<p><%= t :body, "You've been reminded to assess %{rubric}, %{context}:", :rubric => asset.submission.assignment.title, :context => asset.rubric_association.context.name %></p>

