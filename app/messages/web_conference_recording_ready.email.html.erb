<% define_content :link do %>
  <%= polymorphic_url([asset.context, :conferences]) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Web Conference Recording Ready: %{name}", :name => asset.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :details_link, "Click here to see the details"%>
  </a>
<% end %>

<p><%= t :body, "Your recording of %{title} for %{name} is ready.", :title => asset.title, :name => asset.context.name %></p>
