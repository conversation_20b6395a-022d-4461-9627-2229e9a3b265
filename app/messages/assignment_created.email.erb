<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= I18n.t("Assignment Created - %{assignment_name}, %{course_name}", :assignment_name => asset.title, :course_name => asset.context.name) %>
<% end %>

<%= I18n.t("A new assignment has been created for your course, %{course_name}", :course_name => asset.context.name) %>

<%= asset.title %>

<%
  multiple_dates_text = I18n.t("due: Multiple Dates")
  display_text = I18n.t("due: No Due Date")
%>

<% if asset.multiple_due_dates_apply_to?(user) %>
  <% display_text = multiple_dates_text %>
<% elsif asset.due_at %>
  <% display_text = I18n.t("due: %{assignment_due_date_time}", :assignment_due_date_time => datetime_string(force_zone(asset.due_at))) %>
<% else %>
  <% if asset.context.user_has_been_admin?(user) && asset.context.grants_right?(user, :manage_grades) %>
    <% if asset.all_due_dates.length > 1 %>
      <% display_text = multiple_dates_text %>
    <% elsif (asset_due_at = asset.teacher_due_date_for_display(user)) %>
      <% display_text = I18n.t("due: %{assignment_due_date_time}", :assignment_due_date_time => datetime_string(force_zone(asset_due_at))) %>
    <% end %>
  <% end %>
<% end %>

<%= display_text %>
<%= before_label('click_to_see_assignment', 'Click here to view the assignment') %>
<%= content :link %>
