<% define_content :link do %>
  <%= polymorphic_url([asset.assignment.context, asset.assignment, :submission], id: asset.user) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "A new annotation has been made to your submission document" %>
<% end %>

<% if asset.assignment.anonymous_instructor_annotations %>
  <p><%= t "A new annotation has been made on the assignment %{assignment_name}.",
           assignment_name: asset.assignment.name %></p>
<% else %>
  <p><%= t "A new annotation has been made by %{author} on the assignment %{assignment_name}.",
           author: data.author_name, assignment_name: asset.assignment.name %></p>
<% end %>

<%= t("View Submission", link: content(:link)) %>
