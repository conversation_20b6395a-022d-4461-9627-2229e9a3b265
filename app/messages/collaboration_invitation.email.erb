<% define_content :link do %>
  <%= polymorphic_url([asset.collaboration.context, asset.collaboration.becomes(Collaboration)]) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Collaboration Invitation: %{course_or_group}", :course_or_group => asset.collaboration.context.name %>
<% end %>

<%= t :body, <<-BODY, :document => asset.collaboration.title, :course_or_group => asset.collaboration.context.name, :service => asset.collaboration.service_name, :email => asset.user&.gmail, :link => content(:link) unless asset.collaboration.user
You've been invited to collaborate on a document, %{document} for
%{course_or_group}.  The document was created in %{service}
and you were invited using your email address, %{email}.

If that's the wrong email address for this type of collaboration, you'll need to
change your profile settings or register with %{service}.

You can see the details here:
%{link}
BODY
%>

<%= t :body_with_user, <<-BODY, :document => asset.collaboration.title, :course_or_group => asset.collaboration.context.name, :user => asset.collaboration.user.short_name, :service => asset.collaboration.service_name, :email => asset.user&.gmail, :link => content(:link) if asset.collaboration.user
You've been invited to collaborate on a document, %{document} for
%{course_or_group}.  The document was created by %{user}
in %{service} and you were invited using your email address, %{email}.

If that's the wrong email address for this type of collaboration, you'll need to 
change your profile settings or register with %{service}.

You can see the details here:
%{link}
BODY
%>
