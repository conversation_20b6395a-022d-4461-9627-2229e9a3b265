<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= I18n.t("Assignment Created - %{assignment_name}, %{course_name}", :assignment_name => asset.title, :course_name => asset.context.name) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= I18n.t("View the assignment") %>
  </a>
<% end %>

<p><%= I18n.t("A new assignment has been created for your course, %{course_name}",
         :course_name => asset.context.name) %></p>

<hr style="border: 0;
    height: 1px;
    background: #333;
    background-image: -webkit-linear-gradient(left, #ddd, #aaa, #eee);
    background-image:    -moz-linear-gradient(left, #ddd, #aaa, #eee);
    background-image:     -ms-linear-gradient(left, #ddd, #aaa, #eee);
    background-image:      -o-linear-gradient(left, #ddd, #aaa, #eee);" />

<p style="font-weight: bold;"><%= asset.title %></p>

<%
  multiple_dates_text = I18n.t("due: Multiple Dates")
  display_text = I18n.t("due: No Due Date")
%>
<% if asset.multiple_due_dates_apply_to?(user) %>
  <% display_text = multiple_dates_text %>
<% elsif asset.due_at %>
  <% display_text = I18n.t("due: %{assignment_due_date_time}", :assignment_due_date_time => datetime_string(force_zone(asset.due_at))) %>
<% else %>
  <% if asset.context.user_has_been_admin?(user) && asset.context.grants_right?(user, :manage_grades) %>
    <% if asset.all_due_dates.length > 1 %>
      <% display_text = multiple_dates_text %>
    <% elsif (asset_due_at = asset.teacher_due_date_for_display(user)) %>
      <% display_text = I18n.t("due: %{assignment_due_date_time}", :assignment_due_date_time => datetime_string(force_zone(asset_due_at))) %>
    <% end %>
  <% end %>
<% end %>

<p>
  <%= display_text %>
</p>
