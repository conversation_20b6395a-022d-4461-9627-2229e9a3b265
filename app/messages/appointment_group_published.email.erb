<% define_content :link do %>
  <%= appointment_group_url(asset) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'Appointment "%{appointment_name}" is available for signup (%{course})', :appointment_name => asset.title,
        :course => asset.contexts_for_user(user).map(&:name).join(", ")) %>
<% end %>

<%= t :message, 'Time slots for "%{appointment_name}" are now available for signup.', :appointment_name => asset.title %>

<%= before_label :details, "Appointment details" %>
<%= before_label :dates, "Date(s)" %> <%= date_string(asset.start_at, asset.end_at, :no_words) %>
<%= before_label :signup_type, "Signup Type" %> <%=
  asset.participant_type == 'Group' ?
    t(:group_signup, "Group (%{group_category})", :group_category => asset.sub_contexts.first.name) :
    t(:individual_signup, "Individual") %>
<%= before_label :course, "Course" %> <%=
  asset.participant_type == 'Group' ?
    asset.contexts.first.name :
    asset.contexts_for_user(user).map(&:name).join(", ") %>
<% if asset.available_slots -%>
<%= before_label :slots_remaining, "Available time slots" %> <%= asset.available_slots(current_only: true) %>
<% end -%>

<%= t :instructions, "Sign up for a time slot at the following link: %{link}", :link => content(:link) %>
