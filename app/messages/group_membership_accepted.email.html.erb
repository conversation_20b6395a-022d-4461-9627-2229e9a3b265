<% define_content :link do %>
 <%= polymorphic_url([asset.group.context, asset.group]) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'Group Membership Accepted: %{group_name}', :group_name => asset.group.name) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "Click to check out the group" %>
  </a>
<% end %>

<p><%= t('group_request_accepted', 'Group Request Accepted: %{group_name}', :group_name => asset.group.name) %></p>

<p>
<% if asset.group.context.class.to_s == 'Account' %>
<%= t('account_group', "Your request to join the group %{group_name} for the account %{account_name} has been accepted.", :group_name => asset.group.name, :account_name => asset.group.context.name) %>
<% elsif asset.group.context.class.to_s == 'Course' %>
<%= t('course_group', "Your request to join the group %{group_name} for the course %{course_name} has been accepted.", :group_name => asset.group.name, :course_name => asset.group.context.name) %>
<% end %>
</p>
