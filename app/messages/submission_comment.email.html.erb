<% define_content :link do %>
  <% if asset.submission.assignment.context.feature_enabled?(:assignments_2_student) %>
    <%= polymorphic_url([asset.submission.assignment.context, asset.submission.assignment]) %>
  <% else %>
    <%= polymorphic_url([asset.submission.assignment.context, asset.submission.assignment, :submission], id: asset.submission.user) %>
  <% end %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Submission Comment: %{user}, %{assignment}, %{context}",
        :user => submission_comment_submittor(asset, user),
        :assignment => asset.submission.assignment.title,
        :context => asset.submission.assignment.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_message, "Click here to view the comment" %>
  </a>
<% end %>

<p><%= t :body, "%{author} just made a new comment on the submission for %{user} for %{assignment}",
         :author => submission_comment_author(asset, user),
         :user => submission_comment_submittor(asset, user),
         :assignment => asset.submission.assignment.title %></p>

<%= html_to_simple_html(asset.comment,
                        :base_url => dashboard_url,
                        :tags => ['p', 'span', 'h2', 'h3', 'h4' 'ul', 'ol', 'li', 'code'],
                        :attributes => { 
                          'p' => ['style'],
                          'span' => ['style'],
                          'h2' => ['style'],
                          'h3' => ['style'],
                          'h4' => ['style'],
                          'ul' => ['style'],
                          'ol' => ['style'],
                          'li' => ['style'],
                          'code' => ['style'],
                           }) %>

<% if asset.can_read_author?(user, nil) %>
  <% if avatar_enabled? %>
    <table border="0" style="border-collapse: collapse">
      <tr height="30px">
        <td></td>
      </tr>
      <tr >
        <td align="left" width="50" style="width: 50px"><img style="border-radius: 50px; height: 50px; width: 50px;" height="50" width="50" src="<%=author_avatar_url%>" alt="<%=author_short_name%>"> </td>
        <td width="10"></td>
        <td>
          <table border="0" style="font-size: 14px; color: #444444; background-color: #ffffff; font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';" valign="top" align="left">
            <tr>
              <td valign="bottom" align="left">
                <b><%= author_short_name%></b>
              </td>
            </tr>
            <tr>
              <td valign="top" align="left">
                <a href="mailto:<%= author_email_address %>">
                  <%= author_email_address%>
                </a>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  <% else %>
    <p height="30px"></p>
    <p>
      <b><%= author_short_name%></b>
      <br/>
      <a href="mailto:<%= author_email_address %>">
        <%= author_email_address%>
      </a>
    </p>
  <% end %>
<% end %>

<% if asset.media_comment? %>
<p><%= t "#messages.conversation_message.email.audio_comment", "This message includes media comments. To listen or reply, click the link below:" %></p>
<% elsif IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
<p><%= t :submission_comment_link_message, "You can review the submission details using the link below, or can reply to this comment by responding to this message.  When allowed, if you need to include an attachment, please log in to Canvas and reply to the submission." %></p>
<% else %>
<p><%= t "You can review the submission details using the link below." %></p>
<% end %>

<% unless asset.attachments.empty? %>
<div style="margin-top: 20px; padding: 10px; border: 1px solid #f1f1f1; background-color: #f3f3f3;">
  <p style="margin: 0px;"><%= t "#messages.conversation_message.email.attached_files", "Attached Files:" %></p>
  <ul style="padding-left: 20px;">
    <% asset.attachments.each do |attachment| %>
    <li><a href="<%= file_download_url(attachment) %>"><%= attachment.display_name %> - <%= attachment.readable_size %></a></li>
    <% end %>
  </ul>
</div>
<% end %>
