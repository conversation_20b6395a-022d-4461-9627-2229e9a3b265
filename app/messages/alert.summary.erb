<% define_content :link do %>
  <%= course_url data.course_id %>
<% end %>
<% define_content :subject do %>
  <%= t('subject', 'Alert: %{user_name}', :user_name => data.student_name) %>
<% end %>

<%=
  if data.user_id == user.id
    t('body_student', {:one => "An alert has been triggered for you because the following condition has been met:", :other => "An alert has been triggered for you because the following conditions have been met:"}, :count => asset.criteria.length)
  else
    t('body', {:one => "An alert has been triggered for %{student} because the following condition has been met:", :other => "An alert has been triggered for %{student} because the following conditions have been met:"}, :count => asset.criteria.length, :student => data.student_name)
  end
%>
<% asset.criteria.each do |criterion| %>
  <%=
    case criterion.criterion_type
    when 'Interaction'
      t('interaction_description', {:one => 'No student/teacher interaction for one day', :other =>'No student/teacher interaction for %{count} or more days'},:count => n(criterion.threshold))
    when 'UngradedCount'
      t('ungraded_count_description', {:one => 'One or more assignments have not been graded', :other =>'%{count} or more assignments have not been graded'}, :count => n(criterion.threshold))
    when 'UngradedTimespan'
      t('ungraded_timespan_description', {:one => 'One or more submissions have been left ungraded for one or more days', :other =>'One or more submissions have been left ungraded for %{count} or more days'}, :count => n(criterion.threshold))
    end
  %>
<% end %>
