<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
    <%= t :subject, "New Discussion - %{discussion_topic}: %{course}", :discussion_topic => asset.title, :course => asset.context.name %>
<% end %>

<% if !asset.available_for?(user, :check_policies => true) %>
  <%= t("Discussion content is locked or not yet available") %>
<% else %>
  <%= CanvasTextHelper.truncate_text(html_to_text(asset.message, :base_url => dashboard_url), :max_length => 200) %>
<% end %>
