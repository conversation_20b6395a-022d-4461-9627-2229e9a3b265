<% define_content :link do %>
  <%= conversation_url(asset.conversation_id) %>
<% end %>

<% define_content :user_name do %>
  <%= asset.author_short_name_with_shared_contexts(user) rescue t(:unknown_user, "Unknown User") %>
<% end %>

<% define_content :subject do -%>
  <%= t :subject, "%{user_name} just added you to a conversation in Canvas.", :user_name => content(:user_name) %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_text, "View Conversation" %>
  </a>
<% end %>

<p><%= t :body, "%{user_name} just added you to a conversation in Canvas.", :user_name => content(:user_name) %></p>
