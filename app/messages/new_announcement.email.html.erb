<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= before_label asset.title %> <%= asset.context.name %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "View announcement" %>
  </a>
<% end %>

<%= html_to_simple_html(asset.message,
      :base_url => dashboard_url,
      :tags => ['tr', 'td', 'table', 'tbody', 'caption', 'span'],
      :attributes => {
        'table' => [ 'border', 'style', 'cellspacing', 'cellpadding' ],
        'span' => ['style']
      }) %>

<% if asset.attachment %>
<div style="margin-top: 20px; padding: 10px; border: 1px solid #f1f1f1; background-color: #f3f3f3;">
  <p style="margin: 0px;"><%= t :attached_file, "Attached File" %></p>
  <ul style="padding-left: 20px;">
    <li><%= asset.attachment.display_name %> - <%= asset.attachment.readable_size %>
      <%= polymorphic_url([asset.context, :file_download], file_id: asset.attachment) %>
  </ul>
</div>
<% end %>

<% if asset.grants_right?(user, :reply) %>
<p>
  <% if asset.context.is_a? Course %>
    <%= t :announcement_info_course, "Replies to this email will be posted as a reply to the announcement, which will be seen by everyone in the course." %>
  <% else %>
    <%= t :announcement_info_group, "Replies to this email will be posted as a reply to the announcement, which will be seen by everyone in the group." %>
  <% end%>
</p>
<% end %>