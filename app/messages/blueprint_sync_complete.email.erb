<% define_content :link do %>
  <%= polymorphic_url(asset.master_template.course, :anchor => asset.notification_link_anchor) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Blueprint Sync Finished: %{name}", :name => asset.master_template.course.name %>
<% end %>

<%= t :body, "Your blueprint course, %{name}, has synced changes to all associated courses.", :name => asset.master_template.course.name %>

<% if asset.comment.present? %>
"<%= asset.comment %>"
<% end %>

<%= t :details_link, "More info: %{link}", :link => content(:link) %>
