<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= before_label asset.title %> <%= asset.context.name %>
<% end %>

<%= t "You created this Announcement:" %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "View Announcement" %>
  </a>
<% end %>

<%= html_to_simple_html(asset.message,
    :base_url => dashboard_url,
    :tags => ['tr', 'td', 'table', 'tbody', 'caption', 'span'],
    :attributes => {
        'table' => [ 'border', 'style', 'cellspacing', 'cellpadding' ],
        'span' => ['style']
    }) %>

<% if asset.attachment %>
<div style="margin-top: 20px; padding: 10px; border: 1px solid #f1f1f1; background-color: #f3f3f3;">
  <p style="margin: 0px;"><%= t :attached_file, "Attached File" %></p>
  <ul style="padding-left: 20px;">
    <li><%= asset.attachment.display_name %> - <%= asset.attachment.readable_size %>
        <a href="<%= polymorphic_url([asset.context, :file_download], file_id: asset.attachment) %>">[<%= t :download, "download" %>]</a></li>
  </ul>
</div>
<% end %>
