<% define_content :link do %>
  <%= get_submission_link(asset, user) %>
<% end %>

<% define_content :subject do %>
  <%= t('#messages.peer_review_invitation.email.subject', "Assigned peer review for %{reviewee}", :reviewee => reviewee_name(asset, user))%>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link_message, "Click here to complete your review" %>
  </a>
<% end %>

<%= t('#messages.peer_review_invitation.email.body', "You've been invited to peer review %{reviewee}. Follow the link below to review them!", :reviewee => reviewee_name(asset, user))%>
