<% define_content :link do %>
  <%= course_url(asset.course) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Course Enrollment" %>
<% end %>

<% define_content :footer_link do %>
  <% if !asset.user.registered? && asset.user.communication_channel %>
    <a href="<%= registration_confirmation_url(asset.user.communication_channel.confirmation_code, :host => HostUrl.context_host(asset.course)) %>">
      <%= t :complete_registration_link, "Click here to complete registration" %>
    </a>
  <% else %>
    <a href="<%= content(:link) %>">
      <%= t(:link, "Click here to view the course page") %>
    </a>
  <% end %>
<% end %>

<% email = asset.user.email; login = (asset.user.pseudonym.unique_id rescue "none") %>
<% course = "<a href=\"#{content :link}\">#{h asset.course.name}</a>".html_safe %>
<p>
  <%= t "You've been enrolled in the course, %{course}. Course role: %{role_type}", :course => asset.course.name, :role_type => asset.readable_role_name %>
</p>

<table border="0" style="font-size: 14px; color: #444444;
    font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';
    border-collapse: collapse;">
    <tr>
        <td style="padding-right: 10px;"><%= t(:name, 'Name') %>:</td>
        <td style="font-weight: bold;"><%= asset.user.name %></td>
    </tr>
    <tr>
        <td style="padding-right: 10px"><%= t(:email, 'Email') %>:</td>
        <td style="font-weight: bold;"><%= email %></td>
    </tr>
    <% if email != login %>
      <tr>
          <td style="padding-right: 10px;"><%= t(:username, 'Username') %>:</td>
          <td style="font-weight: bold;"><%= asset.user.pseudonym.unique_id rescue t(:none, "none") %></td>
      </tr>
    <% end %>
</table>

<br><br>

<table width="100%" border="0" style="text-align:center;">
  <tr>
    <td>
      <a href="<%= content(:link) %>" style="color: #FFFFFF; background-color: #0374B5; padding: 8px 40px; border-radius: 3px; text-decoration: none; font-weight: bold;">
        <%= t('Get Started') %>
      </a>
    </td>
  </tr>
</table>
