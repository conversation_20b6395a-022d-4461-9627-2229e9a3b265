<% define_content :link do %>
  <%= polymorphic_url([asset.group.context, :groups]) %>
<% end %>
<% define_content :subject do %>
  <%= t('subject', 'Group Membership Rejected: %{group_name}', :group_name => asset.group.name) %>
<% end %>

<%= t('group_request_rejected', 'Group Request Rejected: %{group_name}', :group_name => asset.group.name) %>
<% if asset.group.context.class.to_s == 'Account' %>
<%= t('account_group', "Your request to join the group %{group_name} for the account %{account_name} has been rejected.", :group_name => asset.group.name, :account_name => asset.group.context.name) %>
<% elsif asset.group.context.class.to_s == 'Course' %>
<%= t('course_group', "Your request to join the group %{group_name} for the course %{course_name} has been rejected.", :group_name => asset.group.name, :course_name => asset.group.context.name) %>
<% end %>

<%= t('You can check out other groups by clicking this link: %{link}', link: content(:link)) %>
