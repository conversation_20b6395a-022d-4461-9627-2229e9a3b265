<% presenter = Messages::SubmissionCommentForTeacher::AnnotationPresenter.new(self, data: data) %>

<% define_content :link do %>
  <%= presenter.link.html_safe %>
<% end %>

<% define_content :subject do %>
  <%= presenter.subject %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= presenter.link %>">
    <%= t "Click here to review the submission" %>
  </a>
<% end %>

<p>
  <%= presenter.body %>
</p>
