<% define_content :link do %>
  <%= dashboard_url %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Recent Canvas Notifications" %>
<% end %>

<% define_content :report_date do %>
  <%= (date_string(force_zone(m.send_at), :no_words) rescue t("#date.days.today_lower", "today")) %>
<% end %>

<% m = (delayed_messages || []).find{|m| m.frequency == Notification::FREQ_WEEKLY} || delayed_messages.first %>
<% if !m || m.frequency == Notification::FREQ_DAILY %>
  <p><%= t :daily_body, "You're signed up to receive a daily report of some notifications from your Canvas account.  Below is the report for %{date}:",
           :date => content(:report_date) %></p>
<% elsif m.frequency == Notification::FREQ_WEEKLY %>
  <p><%= t :weekly_body, "You're signed up to receive a weekly report of some notifications from your Canvas account.  Below is the report for the week ending %{date}:",
           :date => content(:report_date) %></p>
<% end %>

<% for delayed_message in delayed_messages do %>
  <p><strong><%= html_to_text(delayed_message.name_of_topic, :base_url => content(:link)) %></strong></p>
  <p><%= html_to_simple_html(delayed_message.summary, :base_url => content(:link)) %></p>
  <p><a href="<%= delayed_message.link %>"><%= t :view, "Click to view" %></a></p>
<% end %>
