<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'New Event - %{event_title}, %{course_name}', :event_title => asset.title, :course_name => asset.context.name) %>
<% end %>

<% if !asset.start_at && !asset.end_at %>
  <%= t('no_time_set', 'No Time Set') %>
<% elsif (asset.start_at == asset.end_at || !asset.end_at) %>
  <%= datetime_string(force_zone(asset.start_at)) %>
<% else %>
  <%= t('from_to_time', 'from %{start_date_time} to %{end_date_time}', :start_date_time => datetime_string(force_zone(asset.start_at)), :end_date_time => datetime_string(force_zone(asset.end_at))) %>
<% end %>
<% if asset.series_uuid %>
  <%= rrule_to_natural_language(asset.rrule) %>
<% end %>
