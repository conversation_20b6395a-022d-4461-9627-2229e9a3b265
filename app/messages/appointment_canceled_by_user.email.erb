<% define_content :link do %>
  <%= appointment_group_url(asset.appointment_group) %>
<% end %>

<% courses = asset.appointment_group.participant_type == 'Group' ?
      asset.appointment_group.contexts.first.name  :
      asset.appointment_group.contexts_for_user(user).map(&:name).join(", ") %>
<% define_content :subject do %>
  <%= t('subject', 'Reservation canceled for "%{appointment_name}" (%{course})', :appointment_name => asset.title, :course => courses) %>
<% end %>

<%= t :message, '%{user} canceled his/her reservation for "%{appointment_name}".', :user => data.updating_user_name || data.updating_user.name, :appointment_name => asset.title %>

<%= before_label :details, "Appointment details" %>
<%= before_label :date_and_time, "Date/time" %> <%= datetime_string(asset.start_at, asset.end_at) %>
<% if asset.appointment_group.participant_type == 'Group' -%>
<%= before_label :attendees, "Attendees" %> <%= asset.participants.map(&:name).to_sentence %>
<%= before_label :group, "Group" %> <%= asset.context.name %>
<% else -%>
<%= before_label :attendee, "Attendee" %> <%= asset.context.name %>
<% end -%>
<%= before_label :course, "Course" %> <%= courses %>
<% if asset.appointment_group.available_slots && asset.grants_right?(user, :read) -%>
<%= before_label :slots_remaining, "Available time slots" %> <%= asset.appointment_group.available_slots(current_only: true) %>
<% end -%>

<%= before_label :cancel_reason, "Reason for canceling" %>
<%= data.cancel_reason || t(:no_reason_given, "none given") %>

<% if asset.grants_right?(user, :read) -%>
<%= t :instructions, "View the appointment at the following link: %{link}", :link => content(:link) %>
<% end -%>
