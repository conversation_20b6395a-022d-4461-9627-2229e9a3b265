<% define_content :link do %>
  <%= appointment_group_url(asset.appointment_group, :event_id => asset.to_param) %>
<% end %>

<% define_content :subject do %>
  <%= t('subject', 'You have been signed up for "%{appointment_name}" (%{course})',
          :appointment_name => asset.title,
          :course => asset.appointment_group.participant_type == 'Group' ?
            asset.appointment_group.contexts.first.name  :
            asset.appointment_group.contexts_for_user(user).map(&:name).join(", ")) %>
<% end %>

<%= t :message, '%{user} has signed you up for "%{appointment_name}" (%{date_and_time}).', :user => data.updating_user_name || data.updating_user.name, :appointment_name => asset.title, :date_and_time => datetime_string(asset.start_at, asset.end_at) %>
