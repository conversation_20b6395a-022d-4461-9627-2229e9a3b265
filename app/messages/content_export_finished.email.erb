<% define_content :link do %>
  <% link = if asset.is_a? WebZipExport %>
      <% course_offline_web_exports_url(asset.course) %>
    <% elsif asset.is_a? EpubExport %>
      <% epub_exports_url %>
    <% else %>
      <% polymorphic_url([asset.context, :content_exports]) %>
    <% end %>
  <%= link %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "Course Export Finished: %{course}", :course => asset.context.name %>
<% end %>

<%= t :body, %{Your content export for "%{course}" has finished.}, :course => asset.context.name %>

<%= content :link %>
