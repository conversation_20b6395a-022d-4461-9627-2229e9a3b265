<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>

<% define_content :subject do %>
  <%= t :subject, "New Discussion - %{discussion_topic}: %{course}", :discussion_topic => asset.title, :course => asset.context.name %>
<%end%>

<% define_content :footer_link do %>
  <a href="<%= content(:link) %>">
    <%= t :link, "Click here to join the conversation" %>
  </a>
<% end %>

<p><%= t :body, "A new discussion has been started that may be interesting to you:" %></p>

<p><strong><%= asset.title %></strong></p>

<% if !asset.available_for?(user, :check_policies => true) %>
  <%= t("Discussion content is locked or not yet available") %>
<% else %>
  <%= html_to_simple_html(asset.message,
  :base_url => dashboard_url,
  :tags => ['tr', 'td', 'table', 'tbody', 'caption'],
  :attributes => { 'table' => [ 'border', 'style', 'cellspacing', 'cellpadding' ] }) %>
<% end %>

<% if asset.attachment %>
  <div style="margin-top: 20px; padding: 10px; border: 1px solid #f1f1f1; background-color: #f3f3f3;">
      <p style="margin: 0px;"><%= t :attached_file, "Attached File" %></p>
      <ul style="padding-left: 20px;">
          <li><%= asset.attachment.display_name %> - <%= asset.attachment.readable_size %>
              <a href="  <%= polymorphic_url([asset.context, :file_download], file_id: asset.attachment) %>">[<%= t :download, "download" %>]</a></li>
      </ul>
  </div>
<% end %>
