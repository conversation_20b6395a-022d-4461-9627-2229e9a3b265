<% define_content :link do %>
  <%= polymorphic_url([asset.context, asset]) %>
<% end %>
  
<% define_content :subject do %>
  <%= t :subject, "Assignment Changed: %{title}, %{course}", :title => asset.title, :course => asset.context.name %>
<% end %>

<%= t :body, <<-BODY, :title => asset.title, :course => asset.context.name, :url => content(:link)
The assignment "%{title}" for %{course} has changed.  

Click here to view the assignment: 
%{url}
BODY
%>