<% presenter = Messages::SubmissionCommentForTeacher::EmailPresenter.new(self) %>

<% define_content :link do %>
  <%= presenter.link.html_safe %>
<% end %>

<% define_content :subject do %>
  <%= presenter.subject %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= presenter.link %>">
    <%= t "Click here to review the submission" %>
  </a>
<% end %>

<p>
  <%= presenter.body %>
</p>

<%= format_message(presenter.comment_text).first %>

<% unless presenter.anonymous? %>
  <% if avatar_enabled? %>
    <table border="0" style="border-collapse: collapse">
      <tr height="30px">
        <td></td>
      </tr>
      <tr >
        <td align="left" width="50" style="width: 50px"><img style="border-radius: 50px; height: 50px; width: 50px;" height="50" width="50" src="<%=author_avatar_url%>" alt="<%=author_short_name%>"> </td>
        <td width="10"></td>
        <td>
          <table border="0" style="font-size: 14px; color: #444444; background-color: #ffffff; font-family: 'Open Sans', 'Lucida Grande', 'Segoe UI', Arial, Verdana, 'Lucida Sans Unicode', Tahoma, 'Sans Serif';" valign="top" align="left">
            <tr>
              <td valign="bottom" align="left">
                <b><%= author_short_name%></b>
              </td>
            </tr>
            <tr>
              <td valign="top" align="left">
                <a href="mailto:<%= author_email_address %>">
                  <%= author_email_address%>
                </a>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  <% else %>
    <p height="30px"></p>
    <p>
      <b><%= author_short_name%></b>
      <br/>
      <a href="mailto:<%= author_email_address %>">
        <%= author_email_address%>
      </a>
    </p>
  <% end %>
<% end %>

<% if presenter.media_comment? %>
<p><%= t "This message includes media comments. To listen or reply, click the link below:" %></p>
<% else %>
<p><%= t "You can review the submission details by clicking the link below:" %></p>
<% end %>

<% unless presenter.attachments.empty? %>
<div style="margin-top: 20px; padding: 10px; border: 1px solid #f1f1f1; background-color: #f3f3f3;">
  <p style="margin: 0px;"><%= t "Attached Files:" %></p>
  <ul style="padding-left: 20px;">
    <% presenter.attachments.each do |attachment| %>
    <li><a href="<%= file_download_url(attachment) %>"><%= attachment.display_name %> - <%= attachment.readable_size %></a></li>
    <% end %>
  </ul>
</div>
<% end %>
