<% define_content :link do %>
  <%= polymorphic_url([asset.group.context, asset.group]) %>
<% end %>
<% define_content :subject do %>
  <%= t :subject, "New Group Membership for %{course_or_account}", :course_or_account => asset.group.context.name %>
<% end %>

<%=
    case asset.group.context_type
    when 'Account'
      t :body_account, "You've been added to a new group for the account %{account}.  The name of the group is %{group_name}.", :account => asset.group.context.name, :group_name => asset.group.name
    else
      t :body_course, "You've been added to a new group for the course %{course}.  The name of the group is %{group_name}.", :course => asset.group.context.name, :group_name => asset.group.name
    end
%>
