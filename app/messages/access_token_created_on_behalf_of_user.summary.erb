<% define_content :link do %>
  <% if user&.account&.primary_domain&.host %>
    <%= url_for({ controller: :profile, action: :settings, host: user.account.primary_domain.host }) %>
  <% else %>
    <%= settings_profile_url %>
  <% end %>
<% end %>

<%= t "A Canvas security token was just generated for your account by someone else. If you requested a user token, no further action is necessary. Before it can be used, the token needs to be activated by you here:" %>
<%= content :link %>
