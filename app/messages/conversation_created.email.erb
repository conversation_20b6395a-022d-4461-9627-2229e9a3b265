<% define_content :link do %>
  <%= conversation_url(asset.conversation_id) %>
<% end %>

<% define_content :user_name do %>
  <%= asset.author_short_name_with_shared_contexts(user) rescue t(:unknown_user, "Unknown User") %>
<% end %>

<% define_content :subject do %>
  <% if IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
    <%= t "%{user_name}, you just sent a message in Canvas.", :user_name => content(:user_name) %>
  <% else %>
    <%= t "%{user_name}, you just sent a message in Canvas - Do not reply", :user_name => content(:user_name) %>
  <% end %>
<% end %>

<% define_content :footer_link do %>
  <a href="<%= content :link %>">
    <% if IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
      <%= t "View this message in Conversations" %>
    <% else %>
      <%= t "Reply to this message in Canvas" %>
    <% end %>
  </a>
<% end %>


<% if asset.conversation.subject.present? %>
  <%= t "Subject: %{subject}", subject: asset.conversation.subject %>
<% end %>

<%= asset.body %>

<% if asset.has_media_objects? %>
  <%= t("This message includes media comments. To listen or reply, click this link: %{link}.", link: content(:link)) %>
<% elsif IncomingMailProcessor::MailboxAccount.reply_to_enabled %>
  <%= t("You can reply to this message in Canvas by replying directly to this email, or by clicking this link: %{link}.  If you need to include an attachment, please log in to Canvas and reply through the Inbox.", link: content(:link)) %>
<% else %>
  <%= t"Please do not reply to this email, it will not be delivered." %>
  <%= t"You can " %><a href="<%= content :link %>"><%= t"reply to this message in Canvas." %></a>
<% end %>

<% unless asset.attachments.empty? %>
  <%= t :attached_files, "Attached Files:" %>
  <% asset.attachments.each do |attachment| %>
    <%= attachment.display_name %> - <%= attachment.readable_size %>
    <%= file_download_url(attachment, verifier: attachment.uuid) %>
  <% end %>
<% end %>
