# frozen_string_literal: true

# Helper module for sending assignment-related notifications
module AssignmentNotificationHelper
  # Sends a notification email to a user about an assignment
  # @param [User] user The user to send the email to
  # @param [String] subject The email subject
  # @param [String] body The email body
  # @param [Course] context The course context
  # @param [String] notification_name The name of the notification type (default: 'late_assignment')
  # @param [Assignment] assignment The assignment being notified about
  # @param [String] action The action that triggered the notification (default: 'email_sent')
  # @param [User] sender The user who triggered the notification (default: nil for system)
  # @return [Boolean] Whether the email was sent successfully
  def self.send_notification_email(
    user,
    subject,
    body,
    context,
    assignment,
    action,
    notification_name = 'late_assignment'
  )
    return false unless user.email_channel

    begin
      # Create a message record
      m = user.email_channel.messages.temp_record
      m.to = user.email_channel.path
      m.context = context
      m.user = user
      m.subject = subject
      m.body = body
      m.notification = Notification.create_or_find_by(name: notification_name, category: 'Due Date')
      m.parse!('email')
            
      # Send the email
      message = Mailer.create_message(m)
      Mailer.deliver(message)
      
      # Log the notification to the assignment auditor
      Auditors::Assignment.record_email_sent(
        assignment,
        user,
        context,
        action
      )
      
      true
    rescue => e
      Rails.logger.error("Failed to send assignment notification: #{e.message}")
      false
    end
  end
  
  # Generates a URL for an assignment
  # @param [Assignment] assignment The assignment
  # @param [Course] course The course the assignment belongs to
  # @return [String] The full URL to the assignment
  def self.assignment_url(assignment, course)
    "#{HostUrl.protocol}://#{HostUrl.default_host}/courses/#{course.id}/assignments/#{assignment.id}"
  end
  
  # Generates a URL for SpeedGrader for an assignment
  # @param [Assignment] assignment The assignment
  # @param [Course] course The course the assignment belongs to
  # @return [String] The full URL to SpeedGrader for the assignment
  def self.speedgrader_url(assignment, course)
    "#{HostUrl.protocol}://#{HostUrl.default_host}/courses/#{course.id}/gradebook/speed_grader?assignment_id=#{assignment.id}"
  end
  
  # Formats a date for display in notifications
  # @param [Time] date The date to format
  # @return [String] The formatted date string
  def self.format_date(date)
    date ? I18n.l(date, format: :long_date_at_time) : I18n.t("No due date")
  end
end
