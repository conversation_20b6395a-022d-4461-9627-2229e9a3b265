# frozen_string_literal: true
module ReportNotificationHelper
  # Sends a notification email to a user about a generated report
  # @param [User] user The user to send the email to
  # @param [String] subject The email subject
  # @param [String] body The email body (summary and link)
  # @param [Account] context The account context
  # @param [String] report_url The download URL for the report
  # @param [String] format The report format (csv/pdf)
  # @param [User] generated_by The user who generated the report
  # @param [Time] generated_at The time the report was generated
  def self.send_report_email(user, subject, body, context, report_url, format, generated_by = nil, generated_at = nil)
    return false unless user.email_channel

    generated_by_info = generated_by ? generated_by.name : "System"
    generated_at_info = generated_at ? generated_at.strftime("%Y-%m-%d %H:%M:%S %Z") : Time.current.strftime("%Y-%m-%d %H:%M:%S %Z")

    begin
      m = user.email_channel.messages.temp_record
      m.to = user.email_channel.path
      m.context = context
      m.user = user
      m.subject = subject
      m.body = <<~BODY
        #{body}

        This report was generated by: #{generated_by_info}
        At: #{generated_at_info}

        The report can be downloaded at:
        #{report_url}
      BODY
      m.notification = Notification.create_or_find_by(name: 'monthly_report_generated', category: 'Reports')
      m.parse!('email')

      message = Mailer.create_message(m)
      Mailer.deliver(message)
      true
    rescue => e
      Rails.logger.error("Failed to send report notification: #{e.message}")
      false
    end
  end
end