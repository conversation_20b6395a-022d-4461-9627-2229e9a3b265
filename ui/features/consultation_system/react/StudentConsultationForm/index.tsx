import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { IconUserLine, IconCalendarMonthLine } from '@instructure/ui-icons'
import ConsultationRequestForm from './ConsultationRequestForm'
import { createConsultationRequest } from '../services/consultationRequestsApi'
import { fetchAvailableDates, fetchAvailableTimes } from '../services/facultyTimeSlotsApi'
import $ from 'jquery'
import type {
  StudentInfo,
  FacultyUser,
  ConsultationRequestFormData,
  AvailableDate,
  AvailableDateTime
} from '../types'

interface StudentConsultationFormProps {
  currentUserId: string
  studentInfo: StudentInfo
  availableFaculty: FacultyUser[]
  concernTypes: string[]
}

const StudentConsultationForm: React.FC<StudentConsultationFormProps> = ({
  currentUserId,
  studentInfo,
  availableFaculty,
  concernTypes
}) => {
  const [loading, setLoading] = useState(false)
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([])
  const [availableTimes, setAvailableTimes] = useState<AvailableDateTime[]>([])
  const [selectedFaculty, setSelectedFaculty] = useState<string>('')
  const [selectedDate, setSelectedDate] = useState<string>('')

  const handleFacultyChange = async (facultyId: string) => {
    setSelectedFaculty(facultyId)
    setSelectedDate('')
    setAvailableDates([])
    setAvailableTimes([])

    if (!facultyId) return

    try {
      setLoading(true)
      const startDate = new Date().toISOString().split('T')[0]
      const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

      const dates = await fetchAvailableDates(startDate, endDate, facultyId)
      setAvailableDates(dates)
    } catch (err: any) {
      $.flashError('Failed to load available dates. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDateChange = async (date: string) => {
    setSelectedDate(date)
    setAvailableTimes([])

    if (!date) return

    try {
      setLoading(true)
      const times = await fetchAvailableTimes(date, selectedFaculty)
      setAvailableTimes(times)
    } catch (err: any) {
      $.flashError('Failed to load available times. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (formData: ConsultationRequestFormData) => {
    setLoading(true)

    const result = await createConsultationRequest(formData)

    if (result.hasError) {
      // Show error toast with specific error message
      $.flashError(result.errorMessage || 'Failed to submit consultation request. Please try again.')
    } else {
      // Show success toast
      $.flashMessage('Your consultation request has been submitted successfully! You will be notified when the faculty member responds.')

      // Reset form
      setSelectedFaculty('')
      setSelectedDate('')
      setAvailableDates([])
      setAvailableTimes([])

      // Navigate to student dashboard after a short delay
      setTimeout(() => {
        window.location.href = '/consultations/student'
      }, 1500)
    }

    setLoading(false)
  }

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconUserLine /> Request Consultation
          </Heading>
          <p>Submit a request for consultation with faculty members. Please provide detailed information about your concern to help faculty prepare for your session.</p>
        </div>

        <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 large 0">
          <Heading level="h3" margin="0 0 small 0">
            Student Information
          </Heading>
          <Flex gap="large">
            <Flex.Item>
              <strong>Name:</strong> {studentInfo.name}
            </Flex.Item>
            <Flex.Item>
              <strong>Student ID:</strong> {studentInfo.student_id}
            </Flex.Item>
          </Flex>
        </View>

        {availableFaculty.length === 0 ? (
          <View as="div" textAlign="center" padding="x-large">
            <div className="empty-state">
              <div className="empty-icon">
                <IconCalendarMonthLine size="large" />
              </div>
              <Heading level="h3" margin="0 0 small 0">
                No Faculty Available
              </Heading>
              <p>There are currently no faculty members with available consultation time slots. Please check back later or contact your academic advisor.</p>
            </div>
          </View>
        ) : (
          <ConsultationRequestForm
            studentInfo={studentInfo}
            availableFaculty={availableFaculty}
            availableDates={availableDates}
            availableTimes={availableTimes}
            selectedFaculty={selectedFaculty}
            selectedDate={selectedDate}
            onFacultyChange={handleFacultyChange}
            onDateChange={handleDateChange}
            onSubmit={handleSubmit}
            loading={loading}
            concernTypes={concernTypes}
          />
        )}

        <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="large 0 0 0">
          <Heading level="h4" margin="0 0 small 0">
            Important Information
          </Heading>
          <ul>
            <li>Consultation requests are typically reviewed within 24-48 hours</li>
            <li>You will receive a notification when your request is approved or declined</li>
            <li>Please arrive on time for your scheduled consultation</li>
            <li>If you need to cancel, please do so at least 2 hours in advance</li>
            <li>For urgent matters, contact your academic advisor directly</li>
          </ul>
        </View>
      </View>
    </div>
  )
}

export default StudentConsultationForm
