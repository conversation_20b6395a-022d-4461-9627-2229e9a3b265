PATH
  remote: ../gems/rubocop-canvas
  specs:
    rubocop-canvas (1.0.0)
      activesupport (>= 7.0)
      jira_ref_parser (= 1.0.1)
      outrigger (~> 3.0, >= 3.0.1)
      railties (~> 7.0)
      rubocop (~> 1.19)
      rubocop-rails (~> 2.19)

GEM
  remote: https://rubygems.org/
  specs:
    actionpack (7.1.5.1)
      actionview (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actionview (7.1.5.1)
      activesupport (= 7.1.5.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activemodel (7.1.5.1)
      activesupport (= 7.1.5.1)
    activerecord (7.1.5.1)
      activemodel (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      timeout (>= 0.4.0)
    activesupport (7.1.5.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    ast (2.4.2)
    base64 (0.2.0)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    builder (3.3.0)
    colorize (1.1.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    crass (1.0.6)
    csv (3.3.2)
    date (3.4.1)
    drb (2.2.1)
    erubi (1.13.1)
    gergich (2.2.1)
      httparty (~> 0.17)
      sqlite3 (>= 1.4, < 3.0)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    io-console (0.8.0)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jira_ref_parser (1.0.1)
    json (2.10.2)
    language_server-protocol (3.17.0.4)
    lint_roller (1.1.0)
    logger (1.6.6)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    minitest (5.25.4)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.3.0)
    nokogiri (1.18.5)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.5-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.5-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.5-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.5-x86_64-linux-gnu)
      racc (~> 1.4)
    outrigger (3.0.3)
      activerecord (>= 6.0, <= 8.0)
      railties (>= 6.0, <= 8.0)
    parallel (1.26.3)
    parser (3.3.7.1)
      ast (~> 2.4.1)
      racc
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.3)
      date
      stringio
    racc (1.8.1)
    rack (3.0.14)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.1.5.1)
      actionpack (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (6.12.0)
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    rubocop (1.74.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.38.1)
      parser (>= 3.3.1.0)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-graphql (1.5.4)
      rubocop (>= 1.50, < 2)
    rubocop-inst (1.2.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-performance (~> 1.24)
    rubocop-performance (1.24.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.72.1, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.30.3)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.72.1, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rake (0.7.1)
      lint_roller (~> 1.1)
      rubocop (>= 1.72.1)
    rubocop-rspec (3.5.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rspec_rails (2.31.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-rspec (~> 3.5)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    sqlite3 (1.7.3)
      mini_portile2 (~> 2.8.0)
    sqlite3 (1.7.3-aarch64-linux)
    sqlite3 (1.7.3-arm64-darwin)
    sqlite3 (1.7.3-x86_64-darwin)
    sqlite3 (1.7.3-x86_64-linux)
    stringio (3.1.3)
    thor (1.3.2)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.6.0)
    zeitwerk (2.7.2)

PLATFORMS
  aarch64-linux
  arm64-darwin
  ruby
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  colorize (~> 1.0)
  gergich (~> 2.1)
  rubocop-canvas!
  rubocop-factory_bot (~> 2.22)
  rubocop-graphql (~> 1.3)
  rubocop-inst (~> 1)
  rubocop-rails (~> 2.19)
  rubocop-rake (~> 0.6)
  rubocop-rspec (~> 3.0)
  rubocop-rspec_rails (~> 2.29)

RUBY VERSION
   ruby 3.3.3p89

BUNDLED WITH
   2.5.10
